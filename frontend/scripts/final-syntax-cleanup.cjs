#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

/**
 * Final syntax cleanup script
 * Fixes remaining double parentheses and JSX syntax errors
 */

class FinalSyntaxCleanup {
    constructor() {
        this.fixedFiles = [];
        this.errors = [];
    }

    // Find all TypeScript and TSX files
    findTsFiles(dir) {
        const pattern = path.join(dir, '**/*.{ts,tsx}');
        return glob.sync(pattern, {
            ignore: [
                '**/node_modules/**',
                '**/dist/**',
                '**/build/**',
                '**/*.d.ts',
                '**/coverage/**'
            ]
        });
    }

    // Fix syntax errors in a file
    fixSyntaxErrors(filePath) {
        try {
            let content = fs.readFileSync(filePath, 'utf8');
            const originalContent = content;
            let fixCount = 0;

            // Define all syntax error patterns and their fixes
            const fixes = [
                // Fix double parentheses in array methods
                {
                    pattern: /\.map\(\(\(([^)]+)\) =>/g,
                    replacement: '.map(($1) =>',
                    description: 'Fixed .map((( double parentheses'
                },
                {
                    pattern: /\.filter\(\(\(([^)]+)\) =>/g,
                    replacement: '.filter(($1) =>',
                    description: 'Fixed .filter((( double parentheses'
                },
                {
                    pattern: /\.forEach\(\(\(([^)]+)\) =>/g,
                    replacement: '.forEach(($1) =>',
                    description: 'Fixed .forEach((( double parentheses'
                },
                {
                    pattern: /\.reduce\(\(\(([^)]+)\) =>/g,
                    replacement: '.reduce(($1) =>',
                    description: 'Fixed .reduce((( double parentheses'
                },
                {
                    pattern: /\.find\(\(\(([^)]+)\) =>/g,
                    replacement: '.find(($1) =>',
                    description: 'Fixed .find((( double parentheses'
                },
                {
                    pattern: /\.some\(\(\(([^)]+)\) =>/g,
                    replacement: '.some(($1) =>',
                    description: 'Fixed .some((( double parentheses'
                },
                {
                    pattern: /\.every\(\(\(([^)]+)\) =>/g,
                    replacement: '.every(($1) =>',
                    description: 'Fixed .every((( double parentheses'
                },

                // Fix JSX syntax errors
                {
                    pattern: /\/>\}/g,
                    replacement: '/>}',
                    description: 'Fixed JSX closing bracket syntax'
                },
                {
                    pattern: /\/>\) \/>/g,
                    replacement: '/>} />',
                    description: 'Fixed JSX multiple closing brackets'
                },

                // Fix decimal number patterns
                {
                    pattern: /\((\d+)\)\.(\d+)/g,
                    replacement: '$1.$2',
                    description: 'Fixed decimal number parentheses'
                },

                // Fix function call parentheses
                {
                    pattern: /\(([a-zA-Z_][a-zA-Z0-9_]*)\)\.([a-zA-Z_][a-zA-Z0-9_]*)/g,
                    replacement: '$1.$2',
                    description: 'Fixed unnecessary parentheses around variables'
                },

                // Fix Object method calls
                {
                    pattern: /Object\.keys([a-zA-Z_$][a-zA-Z0-9_$]*)/g,
                    replacement: 'Object.keys($1)',
                    description: 'Fixed Object.keys missing parentheses'
                },
                {
                    pattern: /Object\.entries([a-zA-Z_$][a-zA-Z0-9_$]*)/g,
                    replacement: 'Object.entries($1)',
                    description: 'Fixed Object.entries missing parentheses'
                },

                // Fix type annotation issues
                {
                    pattern: /\(([^)]+): ([^)]+) =>/g,
                    replacement: '($1: $2) =>',
                    description: 'Fixed function type annotations'
                },

                // Fix excessive "as any" patterns
                {
                    pattern: /\s+as\s+any\s+as\s+any/g,
                    replacement: '',
                    description: 'Fixed double "as any" patterns'
                },

                // Fix malformed useCallback/useEffect
                {
                    pattern: /useCallback\(\(\s*as\s+any\s*\)\s*=>/g,
                    replacement: 'useCallback(() =>',
                    description: 'Fixed useCallback parameters'
                },
                {
                    pattern: /useEffect\(\(\s*as\s+any\s*\)\s*=>/g,
                    replacement: 'useEffect(() =>',
                    description: 'Fixed useEffect parameters'
                },

                // Clean up extra whitespace
                {
                    pattern: /\n\s*\n\s*\n/g,
                    replacement: '\n\n',
                    description: 'Cleaned up extra whitespace'
                }
            ];

            // Apply all fixes
            for (const fix of fixes) {
                const matches = content.match(fix.pattern);
                if (matches) {
                    content = content.replace(fix.pattern, fix.replacement);
                    fixCount += matches.length;
                    console.log(`  ✅ ${fix.description}: ${matches.length} fixes`);
                }
            }

            // Write file if changes were made
            if (content !== originalContent) {
                fs.writeFileSync(filePath, content);
                this.fixedFiles.push(filePath);
                console.log(`✅ Fixed ${fixCount} syntax errors in ${path.relative(process.cwd(), filePath)}`);
                return true;
            } else {
                console.log(`ℹ️ No syntax errors found in ${path.relative(process.cwd(), filePath)}`);
                return false;
            }

        } catch (error) {
            console.error(`❌ Error fixing ${filePath}: ${error.message}`);
            this.errors.push({ file: filePath, error: error.message });
            return false;
        }
    }

    // Run cleanup on all files
    async run() {
        console.log('🔧 Starting final syntax cleanup...\n');

        const srcDir = path.join(__dirname, '..', 'src');
        const tsFiles = this.findTsFiles(srcDir);

        console.log(`📁 Found ${tsFiles.length} TypeScript files\n`);

        let fixedCount = 0;
        for (const file of tsFiles) {
            console.log(`🔍 Checking ${path.relative(process.cwd(), file)}...`);
            if (this.fixSyntaxErrors(file)) {
                fixedCount++;
            }
            console.log(''); // Empty line for readability
        }

        // Summary
        console.log('📊 CLEANUP SUMMARY');
        console.log('==================');
        console.log(`✅ Files processed: ${tsFiles.length}`);
        console.log(`🔧 Files fixed: ${fixedCount}`);
        console.log(`❌ Errors: ${this.errors.length}`);

        if (this.errors.length > 0) {
            console.log('\n❌ ERRORS:');
            this.errors.forEach(error => {
                console.log(`  - ${error.file}: ${error.error}`);
            });
        }

        if (fixedCount > 0) {
            console.log('\n🎉 Syntax cleanup completed successfully!');
            console.log('💡 Run "npm run type-check" to verify all issues are resolved.');
        } else {
            console.log('\n✨ No syntax errors found - code is clean!');
        }
    }
}

// Run the cleanup
const cleanup = new FinalSyntaxCleanup();
cleanup.run().catch(console.error);
