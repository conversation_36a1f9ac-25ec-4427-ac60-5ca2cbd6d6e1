#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

/**
 * Fix JSX syntax errors across the entire codebase
 * Specifically fixes incorrect closing brackets: />) should be />}
 */

class JSXSyntaxFixer {
    constructor() {
        this.fixedFiles = [];
        this.errors = [];
    }

    // Find all TypeScript and TSX files
    findTsFiles(dir) {
        const pattern = path.join(dir, '**/*.{ts,tsx}');
        return glob.sync(pattern, {
            ignore: [
                '**/node_modules/**',
                '**/dist/**',
                '**/build/**',
                '**/*.d.ts',
                '**/coverage/**'
            ]
        });
    }

    // Fix JSX syntax errors in a file
    fixJSXSyntaxErrors(filePath) {
        try {
            let content = fs.readFileSync(filePath, 'utf8');
            const originalContent = content;
            let fixCount = 0;

            // Define JSX syntax error patterns and their fixes
            const jsxFixes = [
                // Fix JSX elements with incorrect closing brackets: />) to />}
                {
                    pattern: /\s*\/>\)/g,
                    replacement: ' />}',
                    description: 'Fixed JSX closing bracket syntax (/>))'
                },

                // Fix JSX elements in conditional rendering: {condition && <Element />)
                {
                    pattern: /(\{[^}]*&&\s*<[^>]*\/>\))/g,
                    replacement: (match) => match.replace(/\/>\)/, '/>}'),
                    description: 'Fixed conditional JSX rendering syntax'
                },

                // Fix JSX elements in ternary expressions: {condition ? <Element1 /> : <Element2 />)
                {
                    pattern: /(\{[^}]*\?\s*<[^>]*\/>\s*:\s*<[^>]*\/>\))/g,
                    replacement: (match) => match.replace(/\/>\)/, '/>}'),
                    description: 'Fixed ternary JSX rendering syntax'
                },

                // Fix JSX elements in array methods: array.map(() => <Element />)
                {
                    pattern: /(\.map\([^)]*=>\s*<[^>]*\/>\))/g,
                    replacement: (match) => match.replace(/\/>\)/, '/>}'),
                    description: 'Fixed array.map JSX syntax'
                },

                // Fix JSX elements in function calls: func(<Element />)
                {
                    pattern: /(\w+\(<[^>]*\/>\))/g,
                    replacement: (match) => {
                        // Only fix if it's not a valid function call
                        if (match.includes('push(') || match.includes('render(') || match.includes('createElement(')) {
                            return match.replace(/\/>\)/, '/>}');
                        }
                        return match;
                    },
                    description: 'Fixed function call JSX syntax'
                }
            ];

            // Apply all JSX fixes
            for (const fix of jsxFixes) {
                const matches = content.match(fix.pattern);
                if (matches) {
                    if (typeof fix.replacement === 'function') {
                        content = content.replace(fix.pattern, fix.replacement);
                    } else {
                        content = content.replace(fix.pattern, fix.replacement);
                    }
                    fixCount += matches.length;
                    console.log(`  ✅ ${fix.description}: ${matches.length} fixes`);
                }
            }

            // Write file if changes were made
            if (content !== originalContent) {
                fs.writeFileSync(filePath, content);
                this.fixedFiles.push(filePath);
                console.log(`✅ Fixed ${fixCount} JSX syntax errors in ${path.relative(process.cwd(), filePath)}`);
                return true;
            } else {
                console.log(`ℹ️ No JSX syntax errors found in ${path.relative(process.cwd(), filePath)}`);
                return false;
            }

        } catch (error) {
            console.error(`❌ Error fixing ${filePath}: ${error.message}`);
            this.errors.push({ file: filePath, error: error.message });
            return false;
        }
    }

    // Run JSX syntax fixes on all files
    async run() {
        console.log('🔧 Starting JSX syntax error fixes...\n');

        const srcDir = path.join(__dirname, '..', 'src');
        const tsFiles = this.findTsFiles(srcDir);

        console.log(`📁 Found ${tsFiles.length} TypeScript files\n`);

        let fixedCount = 0;
        for (const file of tsFiles) {
            console.log(`🔍 Checking ${path.relative(process.cwd(), file)}...`);
            if (this.fixJSXSyntaxErrors(file)) {
                fixedCount++;
            }
            console.log(''); // Empty line for readability
        }

        // Summary
        console.log('📊 JSX SYNTAX FIX SUMMARY');
        console.log('=========================');
        console.log(`✅ Files processed: ${tsFiles.length}`);
        console.log(`🔧 Files fixed: ${fixedCount}`);
        console.log(`❌ Errors: ${this.errors.length}`);

        if (this.errors.length > 0) {
            console.log('\n❌ ERRORS:');
            this.errors.forEach(error => {
                console.log(`  - ${error.file}: ${error.error}`);
            });
        }

        if (fixedCount > 0) {
            console.log('\n🎉 JSX syntax fixes completed successfully!');
            console.log('💡 Run "npm run type-check" to verify all issues are resolved.');
        } else {
            console.log('\n✨ No JSX syntax errors found - code is clean!');
        }
    }
}

// Run the JSX syntax fixer
const fixer = new JSXSyntaxFixer();
fixer.run().catch(console.error);
