#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

/**
 * Verify that all JSX syntax errors have been fixed
 */

class JSXVerifier {
    constructor() {
        this.issues = [];
    }

    // Find all TypeScript and TSX files
    findTsFiles(dir) {
        const pattern = path.join(dir, '**/*.{ts,tsx}');
        return glob.sync(pattern, {
            ignore: [
                '**/node_modules/**',
                '**/dist/**',
                '**/build/**',
                '**/*.d.ts',
                '**/coverage/**'
            ]
        });
    }

    // Check for JSX syntax issues
    checkJSXSyntax(filePath) {
        try {
            const content = fs.readFileSync(filePath, 'utf8');
            const relativePath = path.relative(process.cwd(), filePath);
            
            // Check for common JSX syntax errors
            const checks = [
                {
                    pattern: /\s*\/>\)/g,
                    description: 'Incorrect JSX closing bracket (/>))'
                },
                {
                    pattern: /className="className=/g,
                    description: 'Corrupted className attribute'
                },
                {
                    pattern: /\.push\(<[^>]*\/>\}/g,
                    description: 'Incorrect closing bracket in push() function'
                },
                {
                    pattern: /\{[^}]*&&\s*<[^>]*\/>\)/g,
                    description: 'Incorrect conditional JSX syntax'
                },
                {
                    pattern: /\{[^}]*\?\s*<[^>]*\/>\s*:\s*<[^>]*\/>\)/g,
                    description: 'Incorrect ternary JSX syntax'
                }
            ];

            for (const check of checks) {
                const matches = content.match(check.pattern);
                if (matches) {
                    this.issues.push({
                        file: relativePath,
                        description: check.description,
                        count: matches.length,
                        matches: matches.slice(0, 3) // Show first 3 matches
                    });
                }
            }

        } catch (error) {
            this.issues.push({
                file: path.relative(process.cwd(), filePath),
                description: 'Error reading file',
                error: error.message
            });
        }
    }

    // Run verification
    async run() {
        console.log('🔍 Verifying JSX syntax fixes...\n');

        const srcDir = path.join(__dirname, '..', 'src');
        const tsFiles = this.findTsFiles(srcDir);

        console.log(`📁 Checking ${tsFiles.length} TypeScript files\n`);

        for (const file of tsFiles) {
            this.checkJSXSyntax(file);
        }

        // Report results
        console.log('📊 VERIFICATION RESULTS');
        console.log('=======================');

        if (this.issues.length === 0) {
            console.log('✅ All JSX syntax issues have been resolved!');
            console.log('🎉 No syntax errors found in the codebase.');
        } else {
            console.log(`❌ Found ${this.issues.length} remaining issues:\n`);
            
            this.issues.forEach((issue, index) => {
                console.log(`${index + 1}. ${issue.file}`);
                console.log(`   Issue: ${issue.description}`);
                if (issue.count) {
                    console.log(`   Count: ${issue.count} occurrences`);
                    console.log(`   Examples: ${issue.matches.join(', ')}`);
                }
                if (issue.error) {
                    console.log(`   Error: ${issue.error}`);
                }
                console.log('');
            });
        }

        return this.issues.length === 0;
    }
}

// Run the verification
const verifier = new JSXVerifier();
verifier.run().then(success => {
    process.exit(success ? 0 : 1);
}).catch(console.error);
