#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to recursively find all TypeScript and TSX files
function findTsFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      findTsFiles(fullPath, files);
    } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Function to fix syntax errors in a file
function fixSyntaxErrors(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let changed = false;

  // Fix decimal number syntax errors first
  const decimalNumberPatterns = [
    // Fix (number).decimal to number.decimal
    {
      regex: /\((\d+)\)\.(\d+)/g,
      replacement: '$1.$2'
    }
  ];

  // Apply decimal number fixes first
  for (const pattern of decimalNumberPatterns) {
    const newContent = content.replace(pattern.regex, pattern.replacement);
    if (newContent !== content) {
      content = newContent;
      changed = true;
    }
  }

  // Fix JSX syntax errors
  const jsxPatterns = [
    // Fix JSX elements missing closing bracket: />} to />)
    {
      regex: /\/>\}/g,
      replacement: '/>)'
    }
  ];

  // Apply JSX fixes
  for (const pattern of jsxPatterns) {
    const newContent = content.replace(pattern.regex, pattern.replacement);
    if (newContent !== content) {
      content = newContent;
      changed = true;
    }
  }

  // Fix double parentheses issues (most common problem)
  const doubleParenthesesPatterns = [
    // Fix .map(((param) => to .map((param) =>
    {
      regex: /\.map\(\(\(([^)]+)\) =>/g,
      replacement: '.map(($1) =>'
    },
    // Fix .forEach(((param) => to .forEach((param) =>
    {
      regex: /\.forEach\(\(\(([^)]+)\) =>/g,
      replacement: '.forEach(($1) =>'
    },
    // Fix .filter(((param) => to .filter((param) =>
    {
      regex: /\.filter\(\(\(([^)]+)\) =>/g,
      replacement: '.filter(($1) =>'
    },
    // Fix .reduce(((param) => to .reduce((param) =>
    {
      regex: /\.reduce\(\(\(([^)]+)\) =>/g,
      replacement: '.reduce(($1) =>'
    },
    // Fix .find(((param) => to .find((param) =>
    {
      regex: /\.find\(\(\(([^)]+)\) =>/g,
      replacement: '.find(($1) =>'
    },
    // Fix .some(((param) => to .some((param) =>
    {
      regex: /\.some\(\(\(([^)]+)\) =>/g,
      replacement: '.some(($1) =>'
    },
    // Fix .every(((param) => to .every((param) =>
    {
      regex: /\.every\(\(\(([^)]+)\) =>/g,
      replacement: '.every(($1) =>'
    }
  ];

  // Apply double parentheses fixes first
  for (const pattern of doubleParenthesesPatterns) {
    const newContent = content.replace(pattern.regex, pattern.replacement);
    if (newContent !== content) {
      content = newContent;
      changed = true;
    }
  }

  // Fix missing closing parentheses in function type annotations
  const typeAnnotationPatterns = [
    // Pattern: (param: Type => should be (param: Type) =>
    {
      regex: /\(([^)]+): ([^)]+) =>/g,
      replacement: '($1: $2) =>'
    },
    // Pattern: (...args: T => should be (...args: T) =>
    {
      regex: /\(\.\.\.([^)]+): ([^)]+) =>/g,
      replacement: '(...$1: $2) =>'
    },
    // Pattern: Object.keysobject should be Object.keys(object)
    {
      regex: /Object\.keys([a-zA-Z_$][a-zA-Z0-9_$]*)/g,
      replacement: 'Object.keys($1)'
    },
    // Pattern: Object.entriesobject should be Object.entries(object)
    {
      regex: /Object\.entries([a-zA-Z_$][a-zA-Z0-9_$]*)/g,
      replacement: 'Object.entries($1)'
    },
    // Pattern: render: (item: Type => should be render: (item: Type) =>
    {
      regex: /render: \(([^)]+): ([^)]+) =>/g,
      replacement: 'render: ($1: $2) =>'
    },
    // Pattern: onClick: (item: Type => should be onClick: (item: Type) =>
    {
      regex: /onClick: \(([^)]+): ([^)]+) =>/g,
      replacement: 'onClick: ($1: $2) =>'
    },
    // Pattern: validation?: (value: Type => should be validation?: (value: Type) =>
    {
      regex: /validation\?: \(([^)]+): ([^)]+) =>/g,
      replacement: 'validation?: ($1: $2) =>'
    },
    // Pattern: onSave: (data: Type => should be onSave: (data: Type) =>
    {
      regex: /onSave: \(([^)]+): ([^)]+) =>/g,
      replacement: 'onSave: ($1: $2) =>'
    }
  ];

  // Apply type annotation fixes
  for (const pattern of typeAnnotationPatterns) {
    const newContent = content.replace(pattern.regex, pattern.replacement);
    if (newContent !== content) {
      content = newContent;
      changed = true;
    }
  }
  
  if (changed) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Fixed: ${filePath}`);
    return true;
  }
  
  return false;
}

// Main execution
const srcDir = path.join(__dirname, 'src');
const tsFiles = findTsFiles(srcDir);

console.log(`Found ${tsFiles.length} TypeScript files`);

let fixedCount = 0;
for (const file of tsFiles) {
  if (fixSyntaxErrors(file)) {
    fixedCount++;
  }
}

console.log(`Fixed ${fixedCount} files`);
