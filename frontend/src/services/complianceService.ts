import React from 'react';
/**
 * Advanced Compliance Service
 * Handles regulatory compliance, audit trails, data protection, and governance
 */

import { apiClient } from './api'

export interface ComplianceFramework {
  id: string
  name: string
  description: string
  region: string
  type: 'data_protection' | 'financial' | 'industry' | 'security' | 'labor'
  requirements: ComplianceRequirement[]
  isActive: boolean
  lastAssessment?: Date
  complianceScore: number
}

export interface ComplianceRequirement {
  id: string
  frameworkId: string
  title: string
  description: string
  category: string
  priority: 'low' | 'medium' | 'high' | 'critical'
  status: 'compliant' | 'non_compliant' | 'partial' | 'not_assessed'
  evidence: ComplianceEvidence[]
  controls: ComplianceControl[]
  lastReview: Date
  nextReview: Date
  assignedTo: string
}

export interface ComplianceEvidence {
  id: string
  type: 'document' | 'policy' | 'procedure' | 'training' | 'audit' | 'certification'
  title: string
  description: string
  fileUrl?: string
  uploadedBy: string
  uploadedAt: Date
  expiryDate?: Date
  status: 'valid' | 'expired' | 'pending_review'
}

export interface ComplianceControl {
  id: string
  name: string
  description: string
  type: 'preventive' | 'detective' | 'corrective'
  frequency: 'continuous' | 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually'
  automationLevel: 'manual' | 'semi_automated' | 'fully_automated'
  lastExecution?: Date
  nextExecution?: Date
  effectiveness: number
  owner: string
}

export interface DataProtectionRecord {
  id: string
  dataType: string
  purpose: string
  legalBasis: string
  dataSubjects: string[]
  retentionPeriod: number
  processingActivities: string[]
  thirdPartySharing: boolean
  crossBorderTransfer: boolean
  securityMeasures: string[]
  createdAt: Date
  updatedAt: Date
}

export interface AuditTrail {
  id: string
  userId: string
  action: string
  resource: string
  resourceId: string
  oldValues?: Record<string, any>
  newValues?: Record<string, any>
  ipAddress: string
  userAgent: string
  timestamp: Date
  complianceRelevant: boolean
  retentionDate: Date
}

export interface ComplianceAssessment {
  id: string
  frameworkId: string
  assessmentType: 'self' | 'internal' | 'external' | 'regulatory'
  assessor: string
  startDate: Date
  endDate?: Date
  status: 'planned' | 'in_progress' | 'completed' | 'cancelled'
  findings: ComplianceFinding[]
  overallScore: number
  recommendations: string[]
  nextAssessment?: Date
}

export interface ComplianceFinding {
  id: string
  requirementId: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  finding: string
  recommendation: string
  status: 'open' | 'in_progress' | 'resolved' | 'accepted_risk'
  assignedTo: string
  dueDate: Date
  evidence?: string[]
}

export interface DataSubjectRequest {
  id: string
  type: 'access' | 'rectification' | 'erasure' | 'portability' | 'restriction' | 'objection'
  requesterId: string
  requesterEmail: string
  description: string
  status: 'received' | 'processing' | 'completed' | 'rejected'
  submittedAt: Date
  completedAt?: Date
  assignedTo: string
  response?: string
  documents?: string[]
}

export interface PolicyDocument {
  id: string
  title: string
  category: string
  version: string
  content: string
  approvedBy: string
  approvedAt: Date
  effectiveDate: Date
  reviewDate: Date
  status: 'draft' | 'approved' | 'archived'
  acknowledgments: PolicyAcknowledgment[]
}

export interface PolicyAcknowledgment {
  userId: string
  acknowledgedAt: Date
  version: string
  ipAddress: string
}

class ComplianceService {
  private baseUrl = '/api/compliance'

  // Framework Management
  async getFrameworks(): Promise<ComplianceFramework[]> {
    try {
      const response = await apiClient.get<ComplianceFramework[]>('/compliance/frameworks/')
      return response.data
    } catch (error) {
      console.error('Compliance Service Error:', error)
      return []
    }
  }
  async activateFramework(frameworkId: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/frameworks/${frameworkId}/activate`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to activate framework')
      }
    } catch (error) {
      console.error('Compliance Service Error:', error)
      throw error
    }
  }

  // Requirements Management
  async getRequirements(frameworkId?: string): Promise<ComplianceRequirement[]> {
    try {
      const url = frameworkId
        ? `${this.baseUrl}/requirements?framework=${frameworkId}`
        : `${this.baseUrl}/requirements`

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch requirements')
      }
      return await response.json()
    } catch (error) {
      console.error('Compliance Service Error:', error)
      // No more mock data fallbacks - return empty array if API fails
      return []
    }
  }
  async updateRequirementStatus(requirementId: string, status: string, evidence?: any): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/requirements/${requirementId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ status, evidence })
      })

      if (!response.ok) {
        throw new Error('Failed to update requirement status')
      }
    } catch (error) {
      console.error('Compliance Service Error:', error)
      throw error
    }
  }

  // Data Protection
  async getDataProtectionRecords(): Promise<DataProtectionRecord[]> {
    try {
      const response = await fetch(`${this.baseUrl}/data-protection`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch data protection records')
      }
      return await response.json()
    } catch (error) {
      console.error('Compliance Service Error:', error)
      return this.getMockDataProtectionRecords()
    }
  }
  async createDataProtectionRecord(record: Omit<DataProtectionRecord, 'id' | 'createdAt' | 'updatedAt'>): Promise<DataProtectionRecord> {
    try {
      const response = await fetch(`${this.baseUrl}/data-protection`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(record)
      })

      if (!response.ok) {
        throw new Error('Failed to create data protection record')
      }
      return await response.json()
    } catch (error) {
      console.error('Compliance Service Error:', error)
      throw error
    }
  }

  // Audit Trail
  async getAuditTrail(filters?: any): Promise<AuditTrail[]> {
    try {
      const queryParams = filters ? `?${new URLSearchParamsfilters.toString()}` : ''
      const response = await fetch(`${this.baseUrl}/audit-trail${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch audit trail')
      }
      return await response.json()
    } catch (error) {
      console.error('Compliance Service Error:', error)
      return this.getMockAuditTrail()
    }
  }
  async exportAuditTrail(filters: any, format: 'csv' | 'pdf' | 'json'): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}/audit-trail/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ filters, format })
      })

      if (!response.ok) {
        throw new Error('Failed to export audit trail')
      }
      const result = await response.json()
      return result.downloadUrl
    } catch (error) {
      console.error('Compliance Service Error:', error)
      throw error
    }
  }

  // Assessments
  async getAssessments(): Promise<ComplianceAssessment[]> {
    try {
      const response = await fetch(`${this.baseUrl}/assessments`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch assessments')
      }
      return await response.json()
    } catch (error) {
      console.error('Compliance Service Error:', error)
      return this.getMockAssessments()
    }
  }
  async createAssessment(assessment: Omit<ComplianceAssessment, 'id' | 'findings' | 'overallScore'>): Promise<ComplianceAssessment> {
    try {
      const response = await fetch(`${this.baseUrl}/assessments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(assessment)
      })

      if (!response.ok) {
        throw new Error('Failed to create assessment')
      }
      return await response.json()
    } catch (error) {
      console.error('Compliance Service Error:', error)
      throw error
    }
  }

  // Data Subject Requests
  async getDataSubjectRequests(): Promise<DataSubjectRequest[]> {
    try {
      const response = await apiClient.get<DataSubjectRequest[]>('/compliance/data-subject-requests/')
      return response.data
    } catch (error) {
      console.error('Compliance Service Error:', error)
      return []
    }
  }
  async processDataSubjectRequest(requestId: string, action: string, response?: string): Promise<void> {
    try {
      const apiResponse = await fetch(`${this.baseUrl}/data-subject-requests/${requestId}/process`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ action, response })
      })

      if (!apiResponse.ok) {
        throw new Error('Failed to process data subject request')
      }
    } catch (error) {
      console.error('Compliance Service Error:', error)
      throw error
    }
  }

  // Policy Management
  async getPolicies(): Promise<PolicyDocument[]> {
    try {
      const response = await fetch(`${this.baseUrl}/policies`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch policies')
      }
      return await response.json()
    } catch (error) {
      console.error('Compliance Service Error:', error)
      return []
    }
  }
  async acknowledgePolicy(policyId: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/policies/${policyId}/acknowledge`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to acknowledge policy')
      }
    } catch (error) {
      console.error('Compliance Service Error:', error)
      throw error
    }
  }

  // Compliance Dashboard
  async getComplianceDashboard(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/dashboard`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch compliance dashboard')
      }
      return await response.json()
    } catch (error) {
      console.error('Compliance Service Error:', error)
      return {
        totalPolicies: 0,
        activePolicies: 0,
        pendingReviews: 0,
        complianceScore: 0,
        recentActivity: [],
        upcomingDeadlines: []
      }
    }
  }

}
export const complianceService = new ComplianceService()
export default complianceService
