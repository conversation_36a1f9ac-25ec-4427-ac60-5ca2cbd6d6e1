import { useState, useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import type { AppDispatch, RootState } from './store'
import { verifyToken } from './store/slices/authSlice'
import { fetchNotifications } from './store/slices/notificationSlice'
import { fetchDashboardLayout } from './store/slices/dashboardSlice'
import Layout from './components/Layout'
import Login from './pages/Login'
import Home from './pages/Home'
import HowItWorks from './pages/HowItWorks'
import RoleBasedRouter from './routes/RoleBasedRouter'





function App() {
  const dispatch = useDispatch<AppDispatch>()
  const { isAuthenticated, user, isLoading, token } = useSelector((state: RootState) => state.auth)
  const [language, setLanguage] = useState<'ar' | 'en'>('ar')
  const [hasTriedTokenVerification, setHasTriedTokenVerification] = useState(false)

  // Set document direction based on language
  useEffect(() => {
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr'
    document.documentElement.lang = language
  }, [language])

  // Verify token on app load
  useEffect(() => {
    if (token && !isAuthenticated && !hasTriedTokenVerification) {
      console.log('Attempting token verification...')
      setHasTriedTokenVerification(true)
      dispatch(verifyToken(token))
    } else if (!token) {
      setHasTriedTokenVerification(true)
    }
  }, [dispatch, token, isAuthenticated, hasTriedTokenVerification])

  // Timeout mechanism to prevent infinite loading
  useEffect(() => {
    if (isLoading) {
      const timeout = setTimeout(() => {
        console.log('Loading timeout reached, clearing auth state')
        localStorage.removeItem('token')
        setHasTriedTokenVerification(true)
        window.location.reload()
      }, 5000) // 5 second timeout

      return () => clearTimeout(timeout)
    }
  }, [isLoading])

  // Load user-specific data when authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      dispatch(fetchNotifications(user.role.id))
      dispatch(fetchDashboardLayout(user.role.id))
    }
  }, [dispatch, isAuthenticated, user])

  // Show loading screen while verifying token (but only if we have a token and haven't tried verification yet)
  if (isLoading && token && !hasTriedTokenVerification) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white text-xl">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  return (
    <Router>
      {!isAuthenticated ? (
        <Routes>
          <Route path="/login" element={<Login language={language} />) />
          <Route path="/how-it-works" element={<HowItWorks language={language} setLanguage={setLanguage} />) />
          <Route path="/*" element={<Home language={language} setLanguage={setLanguage} />) />
        </Routes>
      ) : (
        <Layout language={language} setLanguage={setLanguage}>
          <RoleBasedRouter language={language} />
        </Layout>
      )}
    </Router>
  )
}

export default App
