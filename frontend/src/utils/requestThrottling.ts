import React from 'react';
/**
 * PERFORMANCE FIX: Request Throttling Utility
 * Prevents excessive API calls by implementing intelligent throttling
 */

interface ThrottleConfig {
  maxRequests: number
  timeWindow: number // in milliseconds
  cooldownPeriod: number // in milliseconds
}

interface RequestRecord {
  url: string
  timestamp: number
  count: number
}

class RequestThrottler {
  private static instance: RequestThrottler
  private requestHistory: Map<string, RequestRecord[]> = new Map()
  private blockedUrls: Map<string, number> = new Map() // URL -> unblock timestamp
  
  private defaultConfig: ThrottleConfig = {
    maxRequests: 20, // Increased from 5 to 20 for better UX
    timeWindow: 10000, // Increased from 5 to 10 seconds
    cooldownPeriod: 5000 // Reduced from 10 to 5 seconds
  }

  // Special configs for different endpoint types
  private endpointConfigs: Map<string, ThrottleConfig> = new Map([
    // KPI endpoints need higher limits due to dashboard loading
    ['/kpi/', { maxRequests: 50, timeWindow: 30000, cooldownPeriod: 2000 }],
    ['/enhanced/', { maxRequests: 50, timeWindow: 30000, cooldownPeriod: 2000 }],
    ['/hierarchical_access_info/', { maxRequests: 10, timeWindow: 60000, cooldownPeriod: 1000 }],
    // Auth endpoints - reasonable limits for normal usage
    ['/auth/', { maxRequests: 20, timeWindow: 60000, cooldownPeriod: 2000 }],
    // General API endpoints
    ['/api/', { maxRequests: 30, timeWindow: 15000, cooldownPeriod: 3000 }]
  ])

  private constructor() {}

  static getInstance(): RequestThrottler {
    if (!RequestThrottler.instance) {
      RequestThrottler.instance = new RequestThrottler()
    }
    return RequestThrottler.instance
  }

  // Check if request should be allowed
  shouldAllowRequest(url: string, config?: Partial<ThrottleConfig>): boolean {
    // DEVELOPMENT: Bypass throttling for critical endpoints during development
    if (process.env.NODE_ENV === 'development') {
      const bypassPatterns: any = ['/kpi/', '/enhanced/', 'hierarchical_access_info', '/dashboard/', '/auth/login/', '/auth/refresh/']
      if (bypassPatterns.some(pattern => url.includes(pattern))) {
        console.log(`🔄 [DEV] Bypassing throttling for endpoint: ${url}`)
        return true
      }
    }

    // TEMPORARY: Bypass throttling for KPI and hierarchical access endpoints during development
    const kpiPatterns = ['/kpi/', '/enhanced/', 'hierarchical_access_info', '/dashboard/']
    if (kpiPatterns.some(pattern => url.includes(pattern))) {
      console.log(`🔄 Bypassing throttling for KPI endpoint: ${url}`)
      return true
    }

    // Find the most specific endpoint config
    let endpointConfig = this.defaultConfig
    for (const [pattern, patternConfig] of this.endpointConfigs) {
      if (url.includes(pattern)) {
        endpointConfig = patternConfig
        break
      }
    }

    const finalConfig = { ...endpointConfig, ...config }
    const now = Date.now()
    
    // Check if URL is currently blocked
    const unblockTime = this.blockedUrls.get(url)
    if (unblockTime && now < unblockTime) {
      console.warn(`🚫 Request throttled: ${url} (blocked until ${new DateunblockTime.toLocaleTimeString()})`)
      return false
    }

    // Clean up expired blocks
    if (unblockTime && now >= unblockTime) {
      this.blockedUrls.delete(url)
    }

    // Get request history for this URL
    const history = this.requestHistory.get(url) || []
    
    // Clean up old requests outside the time window
    const validRequests = history.filter(record => 
      now - record.timestamp < finalConfig.timeWindow
    )

    // Update history
    this.requestHistory.set(url, validRequests)

    // Check if we've exceeded the limit
    if (validRequests.length >= finalConfig.maxRequests) {
      console.warn(`🚫 Request throttled: ${url} (${validRequests.length}/${finalConfig.maxRequests} requests in ${finalConfig.timeWindow}ms)`)
      
      // Block this URL for the cooldown period
      this.blockedUrls.set(url, now + finalConfig.cooldownPeriod)
      return false
    }

    // Record this request
    validRequests.push({
      url,
      timestamp: now,
      count: validRequests.length + 1
    })

    this.requestHistory.set(url, validRequests)
    return true
  }

  // Get throttling status for debugging
  getThrottleStatus(url: string): {
    isBlocked: boolean
    requestCount: number
    timeUntilUnblock?: number
    recentRequests: RequestRecord[]
  } {
    const now = Date.now()
    const unblockTime = this.blockedUrls.get(url)
    const history = this.requestHistory.get(url) || []
    
    const recentRequests = history.filter(record => 
      now - record.timestamp < this.defaultConfig.timeWindow
    )

    return {
      isBlocked: !!(unblockTime && now < unblockTime),
      requestCount: recentRequests.length,
      timeUntilUnblock: unblockTime && now < unblockTime ? unblockTime - now : undefined,
      recentRequests
    }
  }

  // Clear throttling for a specific URL (for testing/debugging)
  clearThrottling(url: string): void {
    this.requestHistory.delete(url)
    this.blockedUrls.delete(url)
    console.log(`🔄 Cleared throttling for: ${url}`)
  }

  // Clear all throttling (for development/debugging)
  clearAllThrottling(): void {
    this.requestHistory.clear()
    this.blockedUrls.clear()
    console.log('🔄 Cleared all request throttling')
  }

  // Clear throttling for all URLs matching a pattern (for testing/debugging)
  clearThrottlingByPattern(pattern: string): void {
    const urlsToDelete: string[] = []

    // Find URLs that match the pattern
    for (const url of this.requestHistory.keys()) {
      if (url.includes(pattern)) {
        urlsToDelete.push(url)
      }
    }

    for (const url of this.blockedUrls.keys()) {
      if (url.includes(pattern) && !urlsToDelete.includes(url)) {
        urlsToDelete.push(url)
      }
    }

    // Delete matching URLs
    urlsToDelete.forEach(url => {
      this.requestHistory.delete(url)
      this.blockedUrls.delete(url)
    })

    console.log(`🔄 Cleared throttling for ${urlsToDelete.length} URLs matching pattern: ${pattern}`)
  }

  // Get statistics for monitoring
  getStatistics(): {
    totalTrackedUrls: number
    blockedUrls: number
    totalRequests: number
    averageRequestsPerUrl: number
  } {
    const totalTrackedUrls = this.requestHistory.size
    const blockedUrls = this.blockedUrls.size
    const totalRequests = Array.from(this.requestHistory.values())
      .reduce((sum, history) => sum + history.length, 0)
    
    return {
      totalTrackedUrls,
      blockedUrls,
      totalRequests,
      averageRequestsPerUrl: totalTrackedUrls > 0 ? totalRequests / totalTrackedUrls : 0
    }
  }
}

// Export singleton instance
export const requestThrottler = RequestThrottler.getInstance()

// Utility functions for debugging (available in browser console)
if (typeof window !== 'undefined') {
  window.clearKPIThrottling = () => {
    requestThrottler.clearThrottlingByPattern('/kpi/')
    requestThrottler.clearThrottlingByPattern('/enhanced/')
    requestThrottler.clearThrottlingByPattern('hierarchical_access_info')
    console.log('🔄 Cleared all KPI-related throttling')
  }

  window.getThrottleStats = () => {
    const stats = requestThrottler.getStatistics()
    console.log('📊 Throttling Statistics:', stats)
    return stats
  }

  window.clearAllThrottling = () => {
    requestThrottler.clearAllThrottling()
    console.log('🔄 Cleared all request throttling')
  }

  window.debugKPIRequests = () => {
    console.log('🔍 KPI Request Debug Info:')
    console.log('- Throttling bypassed for KPI endpoints: ✅')
    console.log('- Available debug commands:')
    console.log('  • clearKPIThrottling() - Clear KPI throttling')
    console.log('  • getThrottleStats() - Get throttling statistics')
    console.log('  • clearAllThrottling() - Clear all throttling')
    console.log('- Current throttling stats:', requestThrottler.getStatistics())
  }
}

// Utility function to wrap fetch with throttling
export const throttledFetch = async (
  url: string, 
  options?: RequestInit,
  throttleConfig?: Partial<ThrottleConfig>
): Promise<Response> => {
  // Check if request should be allowed
  if (!requestThrottler.shouldAllowRequest(url, throttleConfig)) {
    throw new Error(`Request throttled: ${url}`)
  }

  // Proceed with the request
  return fetch(url, options)
}

// Utility function to create throttled API client
export const createThrottledApiClient = (baseURL: string) => {
  return {
    get: async (endpoint: string, options?: RequestInit) => {
      const url = `${baseURL}${endpoint}`
      return throttledFetch(url, { ...options, method: 'GET' })
    },
    post: async (endpoint: string, data?: any, options?: RequestInit) => {
      const url = `${baseURL}${endpoint}`
      return throttledFetch(url, {
        ...options,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers
        },
        body: data ? JSON.stringify(data) : undefined
      })
    },
    put: async (endpoint: string, data?: any, options?: RequestInit) => {
      const url = `${baseURL}${endpoint}`
      return throttledFetch(url, {
        ...options,
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers
        },
        body: data ? JSON.stringify(data) : undefined
      })
    },
    delete: async (endpoint: string, options?: RequestInit) => {
      const url = `${baseURL}${endpoint}`
      return throttledFetch(url, { ...options, method: 'DELETE' })
    }
  }
}

export default requestThrottler

// Development utilities - expose to window for debugging
if (process.env.NODE_ENV === 'development') {
  ;window.clearLoginThrottling = () => {
    requestThrottler.clearThrottling('http://localhost:8000/api/auth/login/')
    console.log('✅ Login throttling cleared - you can try logging in again')
  }

  ;window.clearAllThrottling = () => {
    requestThrottler.clearAllThrottling()
    console.log('✅ All throttling cleared')
  }

  ;window.getThrottleStatus = (url?: string) => {
    const targetUrl = url || 'http://localhost:8000/api/auth/login/'
    const status = requestThrottler.getThrottleStatus(targetUrl)
    console.log(`🔍 Throttle status for ${targetUrl}:`, status)
    return status
  }
}
