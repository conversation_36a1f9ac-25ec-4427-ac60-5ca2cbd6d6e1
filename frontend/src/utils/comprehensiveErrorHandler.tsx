import React from 'react';
/**
 * ERROR HANDLING FIX: Comprehensive Error Handler
 * Provides unified error handling across the entire application
 */

import { toast } from 'react-hot-toast'
import { normalizeError, AppError, NetworkError } from './errorHandling'

export interface ErrorContext {
  component?: string
  action?: string
  userId?: string
  timestamp?: string
  url?: string
  userAgent?: string
  additionalData?: Record<string, unknown>
}

export interface ErrorHandlingOptions {
  language?: 'ar' | 'en'
  showToast?: boolean
  logToConsole?: boolean
  sendToService?: boolean
  retryable?: boolean
  onRetry?: () => void
  fallbackMessage?: string
}

export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical'

export interface ProcessedError extends AppError {
  severity: ErrorSeverity
  category: string
  userMessage: string
  userMessageAr: string
  actionable: boolean
  retryable: boolean
  context?: ErrorContext
}

class ComprehensiveErrorHandler {
  private errorQueue: ProcessedError[] = []
  private maxQueueSize = 100
  private isOnline = navigator.onLine
  private errorThrottle = new Map<string, number>()
  private readonly THROTTLE_DURATION = 5000 // 5 seconds

  constructor() {
    this.setupGlobalErrorHandlers()
    this.setupNetworkStatusListener()
  }

  // ERROR FIX: Setup global error handlers
  private setupGlobalErrorHandlers(): void {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event: PromiseRejectionEvent) => {
      // Safe console logging
      if (typeof console !== 'undefined' && console.error) {
        try {
          console.error('Unhandled promise rejection:', event.reason)
        } catch (logError) {
          // Fallback if console is broken
        }
      }
      this.handleError(event.reason, {
        component: 'Global',
        action: 'unhandledrejection'
      }, {
        language: 'en',
        showToast: true,
        logToConsole: true
      })
      event.preventDefault()
    })

    // Handle global JavaScript errors with throttling
    window.addEventListener('error', (event) => {
      const errorKey: any = `${event.filename}:${event.lineno}:${event.colno}`
      const now = Date.now()
      const lastTime = this.errorThrottle.get(errorKey) || 0

      // Only log if enough time has passed since last occurrence
      if (now - lastTime > this.THROTTLE_DURATION) {
        // Safe console logging
        if (typeof console !== 'undefined' && console.error) {
          try {
            console.error('Global JavaScript error:', event.error)
          } catch (logError) {
            // Fallback if console is broken
          }
        }
        this.errorThrottle.set(errorKey, now)

        this.handleError(event.error, {
          component: 'Global',
          action: 'javascript_error',
          additionalData: {
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno
          }
        }, {
          language: 'en',
          showToast: false, // Reduce toast spam
          logToConsole: false // Already logged above
        })
      }
    })
  }

  // ERROR FIX: Setup network status listener
  private setupNetworkStatusListener(): void {
    window.addEventListener('online', () => {
      this.isOnline = true
      this.processQueuedErrors()
    })

    window.addEventListener('offline', () => {
      this.isOnline = false
    })
  }

  // ERROR FIX: Process queued errors when back online
  private processQueuedErrors(): void {
    if (this.isOnline && this.errorQueue.length > 0) {
      // Process errors that need to be sent to service
      const errorsToSend = this.errorQueue.filter(error => 
        error.severity === 'critical' || error.severity === 'high'
      )
      
      // Here you would send errors to your logging service
      // For now, just log them
      if (errorsToSend.length > 0) {
        console.log('Processing queued errors:', errorsToSend)
      }
    }
  }

  // ERROR FIX: Main error handling method
  handleError(
    error: unknown,
    context: ErrorContext = {},
    options: ErrorHandlingOptions = {}): ProcessedError {
    const {
      language = 'en',
      showToast = true,
      logToConsole = true,
      sendToService = false,
      retryable = false,
      onRetry,
      fallbackMessage
    } = options

    // Normalize the error
    const normalizedError = normalizeError(error)
    
    // Process the error
    const processedError = this.processError(normalizedError, context, language)
    
    // Add to error queue
    this.addToErrorQueue(processedError)

    // Handle based on severity
    this.handleBySeverity(processedError, {
      showToast,
      logToConsole,
      sendToService,
      retryable,
      onRetry,
      fallbackMessage,
      language
    })

    return processedError
  }

  // ERROR FIX: Process error into structured format
  private processError(
    error: AppError,
    context: ErrorContext,
    language: 'ar' | 'en'
  ): ProcessedError {
    const severity = this.determineSeverity(error)
    const category = this.categorizeError(error)
    const { userMessage, userMessageAr } = this.generateUserMessages(error, language)

    return {
      ...error,
      severity,
      category,
      userMessage,
      userMessageAr,
      actionable: this.isActionable(error),
      retryable: this.isRetryable(error),
      context: {
        ...context,
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: navigator.userAgent
      }
    }
  }

  // ERROR FIX: Determine error severity
  private determineSeverity(error: AppError): ErrorSeverity {
    if (error instanceof NetworkError) {
      if (error.status >= 500) return 'critical'
      if (error.status >= 400) return 'high'
      return 'medium'
    }

    if (error.message.toLowerCase().includes('network')) return 'high'
    if (error.message.toLowerCase().includes('auth')) return 'high'
    if (error.message.toLowerCase().includes('permission')) return 'medium'
    
    return 'low'
  }

  // ERROR FIX: Categorize error type
  private categorizeError(error: AppError): string {
    if (error instanceof NetworkError) return 'Network'
    if (error.message.toLowerCase().includes('auth')) return 'Authentication'
    if (error.message.toLowerCase().includes('permission')) return 'Authorization'
    if (error.message.toLowerCase().includes('validation')) return 'Validation'
    if (error.message.toLowerCase().includes('network')) return 'Network'
    
    return 'Application'
  }

  // ERROR FIX: Generate user-friendly messages
  private generateUserMessages(error: AppError, language: 'ar' | 'en'): {
    userMessage: string
    userMessageAr: string
  } {
    const messages = {
      network: {
        en: 'Network connection issue. Please check your internet connection.',
        ar: 'مشكلة في الاتصال بالشبكة. يرجى التحقق من اتصالك بالإنترنت.'
      },
      auth: {
        en: 'Authentication failed. Please log in again.',
        ar: 'فشل في المصادقة. يرجى تسجيل الدخول مرة أخرى.'
      },
      permission: {
        en: 'You do not have permission to perform this action.',
        ar: 'ليس لديك صلاحية لتنفيذ هذا الإجراء.'
      },
      validation: {
        en: 'Please check your input and try again.',
        ar: 'يرجى التحقق من المدخلات والمحاولة مرة أخرى.'
      },
      default: {
        en: 'An unexpected error occurred. Please try again.',
        ar: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.'
      }
    }

    let messageKey = 'default'
    const errorMsg = error.message.toLowerCase()
    
    if (errorMsg.includes('network') || error instanceof NetworkError) messageKey = 'network'
    else if (errorMsg.includes('auth')) messageKey = 'auth'
    else if (errorMsg.includes('permission')) messageKey = 'permission'
    else if (errorMsg.includes('validation')) messageKey = 'validation'

    return {
      userMessage: messages[messageKey as keyof typeof messages].en,
      userMessageAr: messages[messageKey as keyof typeof messages].ar
    }
  }

  // ERROR FIX: Check if error is actionable
  private isActionable(error: AppError): boolean {
    const actionablePatterns = ['network', 'auth', 'validation', 'permission']
    return actionablePatterns.some(pattern => 
      error.message.toLowerCase().includes(pattern)
    )
  }

  // ERROR FIX: Check if error is retryable
  private isRetryable(error: AppError): boolean {
    if (error instanceof NetworkError) {
      return error.status >= 500 || error.status === 408 || error.status === 429
    }
    
    const retryablePatterns = ['network', 'timeout', 'temporary']
    return retryablePatterns.some(pattern => 
      error.message.toLowerCase().includes(pattern)
    )
  }

  // ERROR FIX: Add error to queue
  private addToErrorQueue(error: ProcessedError): void {
    this.errorQueue.push(error)

    // Maintain queue size
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift()
    }
  }

  // ERROR FIX: Handle error based on severity
  private handleBySeverity(
    error: ProcessedError,
    options: ErrorHandlingOptions & { language: 'ar' | 'en' }
  ): void {
    const { showToast, logToConsole, language, retryable, onRetry } = options

    if (logToConsole) {
      // Safe console logging
      if (typeof console !== 'undefined' && console.error) {
        try {
          console.error(`[${error.severity.toUpperCase()}] ${error.category}:`, error)
        } catch (logError) {
          // Fallback if console is broken
        }
      }
    }

    if (showToast) {
      const message = language === 'ar' ? error.userMessageAr : error.userMessage

      switch (error.severity) {
        case 'critical':
          toast.error(message, {
            duration: 8000,
            position: language === 'ar' ? 'top-left' : 'top-right'
          })
          break
        case 'high':
          toast.error(message, {
            duration: 6000,
            position: language === 'ar' ? 'top-left' : 'top-right'
          })
          break
        case 'medium':
          toast.error(message, {
            duration: 4000,
            position: language === 'ar' ? 'top-left' : 'top-right'
          })
          break
        case 'low':
          toast(message, {
            duration: 3000,
            position: language === 'ar' ? 'top-left' : 'top-right'
          })
          break
      }

      // Add retry button for retryable errors
      if (retryable && onRetry) {
        toast((t) => (
          <div className="flex items-center gap-2">
            <span>{language === 'ar' ? 'إعادة المحاولة؟' : 'Retry?'}</span>
            <button
              onClick={() => {
                toast.dismiss(t.id)
                onRetry()
              }}
              className="px-2 py-1 bg-blue-500 text-white rounded text-sm"
            >
              {language === 'ar' ? 'نعم' : 'Yes'}
            </button>
          </div>
        ), {
          duration: 10000,
          position: language === 'ar' ? 'top-left' : 'top-right'
        })
      }
    }
  }

  // ERROR FIX: Get error statistics
  getErrorStats() {
    const bySeverity = this.errorQueue.reduce((acc, error) => {
      acc[error.severity] = (acc[error.severity] || 0) + 1
      return acc
    }, {} as Record<ErrorSeverity, number>)

    const byCategory = this.errorQueue.reduce((acc, error) => {
      acc[error.category] = (acc[error.category] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const recent = this.errorQueue.slice(-10)

    return {
      total: this.errorQueue.length,
      bySeverity,
      byCategory,
      recent
    }
  }

  clearErrorQueue() {
    this.errorQueue = []
  }

  isSystemHealthy() {
    const criticalErrors = this.errorQueue.filter(e => e.severity === 'critical').length
    const highErrors = this.errorQueue.filter(e => e.severity === 'high').length

    // System is unhealthy if there are too many critical/high errors
    return criticalErrors < 5 && highErrors < 10
  }
}

// Export singleton instance
export const comprehensiveErrorHandler = new ComprehensiveErrorHandler()

// ERROR FIX: React hook for error handling
export function useErrorHandler(language: 'ar' | 'en' = 'en') {
  const handleError = (
    error: unknown,
    context: ErrorContext = {},
    options: Partial<ErrorHandlingOptions> = {}
  ): void => {
    return comprehensiveErrorHandler.handleError(error, context, {
      language,
      ...options
    })
  }

  const handleAsyncError = async (
    asyncFn: () => Promise<any>,
    context: ErrorContext = {},
    options: Partial<ErrorHandlingOptions> = {}
  ) => {
    try {
      return await asyncFn()
    } catch (error) {
      handleError(error, context, options)
      throw error
    }
  }

  return {
    handleError,
    handleAsyncError,
    getErrorStats: () => comprehensiveErrorHandler.getErrorStats(),
    isSystemHealthy: () => comprehensiveErrorHandler.isSystemHealthy()
  }
}
