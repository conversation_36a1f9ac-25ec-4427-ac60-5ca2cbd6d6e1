import { Routes, Route, Navigate } from 'react-router-dom'
import RoleBasedRoute from '../components/RoleBasedRoute'
import Unauthorized from '../pages/Unauthorized'

// Sales Manager Dashboard
import SalesManagerDashboard from '../pages/dashboards/SalesManagerDashboard'

// Sales Manager Specific Pages
import SalesCustomers from '../pages/sales-specific/SalesCustomers'
import Sales from '../pages/sales/Sales'
import Products from '../pages/products/Products'

// Shared Pages (accessible by Sales Manager)
import Reports from '../pages/Reports'
import Settings from '../pages/Settings'

// Analytics
import AdvancedAnalytics from '../pages/analytics/AdvancedAnalytics'

// Communication
import Messages from '../pages/communication/Messages'
import Announcements from '../pages/communication/Announcements'

// Personal Features
import PersonalProfile from '../pages/personal/PersonalProfile'
import PersonalMessages from '../pages/personal/PersonalMessages'
import PersonalCalendar from '../pages/personal/PersonalCalendar'

interface SalesManagerRoutesProps {
  language: 'ar' | 'en'
}

export default function SalesManagerRoutes({ language }: SalesManagerRoutesProps) {
  return (
    <Routes>
      {/* Sales Manager Dashboard */}
      <Route path="/" element={
        <RoleBasedRoute requiredRole="sales_manager" fallbackPath="/sales/unauthorized">
          <SalesManagerDashboard language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/sales/dashboard" element={
        <RoleBasedRoute requiredRole="sales_manager" fallbackPath="/sales/unauthorized">
          <SalesManagerDashboard language={language} />
        </RoleBasedRoute>
      } />

      {/* Sales Management */}
      <Route path="/sales/orders" element={
        <RoleBasedRoute requiredRole="sales_manager" fallbackPath="/sales/unauthorized">
          <Sales language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/sales/customers" element={
        <RoleBasedRoute requiredRole="sales_manager" fallbackPath="/sales/unauthorized">
          <SalesCustomers language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/sales/products" element={
        <RoleBasedRoute requiredRole="sales_manager" fallbackPath="/sales/unauthorized">
          <Products language={language} />
        </RoleBasedRoute>
      } />

      {/* Analytics & Reports */}
      <Route path="/sales/analytics" element={
        <RoleBasedRoute requiredRole="sales_manager" fallbackPath="/sales/unauthorized">
          <AdvancedAnalytics language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/sales/reports" element={
        <RoleBasedRoute requiredRole="sales_manager" fallbackPath="/sales/unauthorized">
          <Reports language={language} />
        </RoleBasedRoute>
      } />

      {/* Personal Features */}
      <Route path="/sales/profile" element={
        <RoleBasedRoute requiredRole="sales_manager" fallbackPath="/sales/unauthorized">
          <PersonalProfile language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/sales/messages" element={
        <RoleBasedRoute requiredRole="sales_manager" fallbackPath="/sales/unauthorized">
          <PersonalMessages language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/sales/calendar" element={
        <RoleBasedRoute requiredRole="sales_manager" fallbackPath="/sales/unauthorized">
          <PersonalCalendar language={language} />
        </RoleBasedRoute>
      } />

      {/* Communication */}
      <Route path="/sales/communication/messages" element={
        <RoleBasedRoute requiredRole="sales_manager" fallbackPath="/sales/unauthorized">
          <Messages language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/sales/communication/announcements" element={
        <RoleBasedRoute requiredRole="sales_manager" fallbackPath="/sales/unauthorized">
          <Announcements language={language} />
        </RoleBasedRoute>
      } />

      {/* Settings */}
      <Route path="/sales/settings" element={
        <RoleBasedRoute requiredRole="sales_manager" fallbackPath="/sales/unauthorized">
          <Settings language={language} />
        </RoleBasedRoute>
      } />

      {/* Unauthorized Access */}
      <Route path="/sales/unauthorized" element={<Unauthorized language={language} />) />

      {/* Catch all route */}
      <Route path="*" element={<Navigate to="/sales/dashboard" replace />) />
    </Routes>
  )
}
