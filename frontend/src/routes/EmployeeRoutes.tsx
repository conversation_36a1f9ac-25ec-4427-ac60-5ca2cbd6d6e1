import { Routes, Route, Navigate } from 'react-router-dom'
import RoleBasedRoute from '../components/RoleBasedRoute'
import Unauthorized from '../pages/Unauthorized'

// Employee Dashboard
import EmployeeDashboard from '../pages/dashboards/EmployeeDashboard'

// Employee Pages (Employee-specific versions)
import EmployeeTasks from '../pages/employee-specific/EmployeeTasks'
import EmployeeProfile from '../pages/employee-specific/EmployeeProfile'
import EmployeeLeave from '../pages/employee-specific/EmployeeLeave'
import Projects from '../pages/projects/Projects'

// Communication (Available to all)
import Messages from '../pages/communication/Messages'
import Announcements from '../pages/communication/Announcements'
import Documents from '../pages/communication/Documents'
import Meetings from '../pages/communication/Meetings'

// Personal Features
import PersonalProfile from '../pages/personal/PersonalProfile'
import PersonalMessages from '../pages/personal/PersonalMessages'
import PersonalCalendar from '../pages/personal/PersonalCalendar'

interface EmployeeRoutesProps {
  language: 'ar' | 'en'
}

export default function EmployeeRoutes({ language }: EmployeeRoutesProps) {
  return (
    <Routes>
      {/* Dashboard */}
      <Route path="/" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <EmployeeDashboard language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/employee/dashboard" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <EmployeeDashboard language={language} />
        </RoleBasedRoute>
      } />

      {/* Employee Self-Service */}
      <Route path="/employee/profile" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <EmployeeProfile language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/employee/leave" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <EmployeeLeave language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/employee/tasks" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <EmployeeTasks language={language} />
        </RoleBasedRoute>
      } />

      {/* Project Management (Employee-specific) */}
      <Route path="/employee/projects" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <Projects language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/employee/projects/tasks" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <EmployeeTasks language={language} />
        </RoleBasedRoute>
      } />

      {/* Personal Features */}
      <Route path="/employee/profile" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <PersonalProfile language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/employee/messages" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <PersonalMessages language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/employee/calendar" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <PersonalCalendar language={language} />
        </RoleBasedRoute>
      } />

      {/* Communication */}
      <Route path="/employee/communication/messages" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <Messages language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/employee/communication/announcements" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <Announcements language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/employee/communication/documents" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <Documents language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/employee/communication/meetings" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <Meetings language={language} />
        </RoleBasedRoute>
      } />

      {/* Unauthorized Access */}
      <Route path="/employee/unauthorized" element={<Unauthorized language={language} />) />

      {/* Catch all - redirect to employee dashboard */}
      <Route path="*" element={<Navigate to="/employee/dashboard" replace />) />
    </Routes>
  )
}
