import { Routes, Route, Navigate } from 'react-router-dom'
import RoleBasedRoute from '../components/RoleBasedRoute'
import Unauthorized from '../pages/Unauthorized'

// Department Manager Dashboard
import DepartmentManagerDashboard from '../pages/dashboards/DepartmentManagerDashboard'

// Department Manager Pages
import Departments from '../pages/Departments'
import Reports from '../pages/Reports'

// Project Management (Department-specific versions)
import DepartmentProjects from '../pages/department-specific/DepartmentProjects'
import DepartmentCustomers from '../pages/department-specific/DepartmentCustomers'
import Tasks from '../pages/projects/Tasks'
import ProjectReports from '../pages/projects/ProjectReports'

// Communication (Available to all)
import Messages from '../pages/communication/Messages'
import Announcements from '../pages/communication/Announcements'
import Documents from '../pages/communication/Documents'
import Meetings from '../pages/communication/Meetings'

// Analytics
import AdvancedAnalytics from '../pages/analytics/AdvancedAnalytics'

// Personal Features
import PersonalProfile from '../pages/personal/PersonalProfile'
import PersonalMessages from '../pages/personal/PersonalMessages'
import PersonalCalendar from '../pages/personal/PersonalCalendar'

interface DepartmentManagerRoutesProps {
  language: 'ar' | 'en'
}

export default function DepartmentManagerRoutes({ language }: DepartmentManagerRoutesProps) {
  return (
    <Routes>
      {/* Dashboard */}
      <Route path="/" element={
        <RoleBasedRoute requiredRole="department_manager" fallbackPath="/department/unauthorized">
          <DepartmentManagerDashboard language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/department/dashboard" element={
        <RoleBasedRoute requiredRole="department_manager" fallbackPath="/department/unauthorized">
          <DepartmentManagerDashboard language={language} />
        </RoleBasedRoute>
      } />

      {/* Department Management */}
      <Route path="/department/departments" element={
        <RoleBasedRoute requiredRole="department_manager" fallbackPath="/department/unauthorized">
          <Departments language={language} />
        </RoleBasedRoute>
      } />

      {/* Project Management */}
      <Route path="/department/projects" element={
        <RoleBasedRoute requiredRole="department_manager" fallbackPath="/department/unauthorized">
          <DepartmentProjects language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/department/tasks" element={
        <RoleBasedRoute requiredRole="department_manager" fallbackPath="/department/unauthorized">
          <Tasks language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/department/reports" element={
        <RoleBasedRoute requiredRole="department_manager" fallbackPath="/department/unauthorized">
          <ProjectReports language={language} />
        </RoleBasedRoute>
      } />

      {/* Personal Features */}
      <Route path="/department/profile" element={
        <RoleBasedRoute requiredRole="department_manager" fallbackPath="/department/unauthorized">
          <PersonalProfile language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/department/messages" element={
        <RoleBasedRoute requiredRole="department_manager" fallbackPath="/department/unauthorized">
          <PersonalMessages language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/department/calendar" element={
        <RoleBasedRoute requiredRole="department_manager" fallbackPath="/department/unauthorized">
          <PersonalCalendar language={language} />
        </RoleBasedRoute>
      } />

      {/* Communication */}
      <Route path="/department/communication/messages" element={
        <RoleBasedRoute requiredRole="department_manager" fallbackPath="/department/unauthorized">
          <Messages language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/department/communication/announcements" element={
        <RoleBasedRoute requiredRole="department_manager" fallbackPath="/department/unauthorized">
          <Announcements language={language} />
        </RoleBasedRoute>
      } />

      {/* Unauthorized Access */}
      <Route path="/department/unauthorized" element={<Unauthorized language={language} />) />

      {/* Catch all - redirect to department dashboard */}
      <Route path="*" element={<Navigate to="/department/dashboard" replace />) />
    </Routes>
  )
}
