import { Routes, Route, Navigate } from 'react-router-dom'
import RoleBasedRoute from '../components/RoleBasedRoute'
import Unauthorized from '../pages/Unauthorized'

// Finance Manager Dashboard
import FinanceManagerDashboard from '../pages/dashboards/FinanceManagerDashboard'

// Finance Manager Pages (Finance-specific versions)
import FinanceReports from '../pages/finance-specific/FinanceReports'
import FinanceCustomers from '../pages/finance-specific/FinanceCustomers'

// Financial Management (Finance-specific versions)
import FinanceBudgets from '../pages/finance-specific/FinanceBudgets'
import Expenses from '../pages/finance-specific/FinanceBudgets' // Reuse budgets component
import FinancialReports from '../pages/finance-specific/FinanceReports'

// Asset Management (Finance Manager can access all asset features)
import Assets from '../pages/assets/Assets'
import Suppliers from '../pages/assets/Suppliers'
import PurchaseOrders from '../pages/assets/PurchaseOrders'

// Communication (Available to all)
import Messages from '../pages/communication/Messages'
import Announcements from '../pages/communication/Announcements'
import Documents from '../pages/communication/Documents'
import Meetings from '../pages/communication/Meetings'

// Analytics
import AdvancedAnalytics from '../pages/analytics/AdvancedAnalytics'

// Personal Features
import PersonalProfile from '../pages/personal/PersonalProfile'
import PersonalMessages from '../pages/personal/PersonalMessages'
import PersonalCalendar from '../pages/personal/PersonalCalendar'

interface FinanceManagerRoutesProps {
  language: 'ar' | 'en'
}

export default function FinanceManagerRoutes({ language }: FinanceManagerRoutesProps) {
  return (
    <Routes>
      {/* Dashboard */}
      <Route path="/" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <FinanceManagerDashboard language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/finance/dashboard" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <FinanceManagerDashboard language={language} />
        </RoleBasedRoute>
      } />

      {/* Financial Management */}
      <Route path="/finance/budgets" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <FinanceBudgets language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/finance/expenses" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <Expenses language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/finance/reports" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <FinancialReports language={language} />
        </RoleBasedRoute>
      } />

      {/* Asset Management */}
      <Route path="/finance/assets" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <Assets language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/finance/suppliers" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <Suppliers language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/finance/purchase-orders" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <PurchaseOrders language={language} />
        </RoleBasedRoute>
      } />

      {/* Inventory */}
      <Route path="/finance/inventory" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <Assets language={language} />
        </RoleBasedRoute>
      } />

      {/* Personal Features */}
      <Route path="/finance/profile" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <PersonalProfile language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/finance/messages" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <PersonalMessages language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/finance/calendar" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <PersonalCalendar language={language} />
        </RoleBasedRoute>
      } />

      {/* Communication */}
      <Route path="/finance/communication/messages" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <Messages language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/finance/communication/announcements" element={
        <RoleBasedRoute requiredRole="finance_manager" fallbackPath="/finance/unauthorized">
          <Announcements language={language} />
        </RoleBasedRoute>
      } />

      {/* Unauthorized Access */}
      <Route path="/finance/unauthorized" element={<Unauthorized language={language} />) />

      {/* Catch all - redirect to finance dashboard */}
      <Route path="*" element={<Navigate to="/finance/dashboard" replace />) />
    </Routes>
  )
}
