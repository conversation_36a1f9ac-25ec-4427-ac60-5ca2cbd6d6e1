import { Routes, Route, Navigate } from 'react-router-dom'
import RoleBasedRoute from '../components/RoleBasedRoute'
import Unauthorized from '../pages/Unauthorized'

// Super Admin Dashboard
import SuperAdminDashboard from '../pages/dashboards/SuperAdminDashboard'

// Super Admin Pages
import Employees from '../pages/Employees'
import Departments from '../pages/Departments'
import Reports from '../pages/Reports'
import Settings from '../pages/Settings'

// HR Management (Super Admin can access all HR features)
import LeaveManagement from '../pages/hr/LeaveManagement'
import Attendance from '../pages/hr/Attendance'
import Performance from '../pages/hr/Performance'
import Payroll from '../pages/hr/Payroll'

// Project Management (Super Admin can access all project features)
import Projects from '../pages/projects/Projects'
import Tasks from '../pages/projects/Tasks'
import ProjectReports from '../pages/projects/ProjectReports'

// Financial Management (Super Admin can access all finance features)
import Budgets from '../pages/finance-specific/FinanceBudgets'
import Expenses from '../pages/finance-specific/FinanceBudgets' // Reuse budgets component
import FinancialReports from '../pages/finance-specific/FinanceReports'

// Asset Management (Super Admin can access all asset features)
import Assets from '../pages/assets/Assets'
import Suppliers from '../pages/assets/Suppliers'
import PurchaseOrders from '../pages/assets/PurchaseOrders'

// Communication (Available to all)
import Messages from '../pages/communication/Messages'
import Announcements from '../pages/communication/Announcements'
import Documents from '../pages/communication/Documents'
import Meetings from '../pages/communication/Meetings'

// Personal Pages
import PersonalProfile from '../pages/personal/PersonalProfile'
import PersonalMessages from '../pages/personal/PersonalMessages'
import PersonalCalendar from '../pages/personal/PersonalCalendar'

// Analytics
import AdvancedAnalytics from '../pages/analytics/AdvancedAnalytics'

// Employee Self-Service (Super Admin can access all)
import EmployeeProfile from '../pages/employee-specific/EmployeeProfile'
import EmployeeLeave from '../pages/employee-specific/EmployeeLeave'
import EmployeeTasks from '../pages/employee-specific/EmployeeTasks'

// Inventory Management
import Inventory from '../pages/inventory/Inventory'

// System Administration
import UserManagement from '../pages/admin/UserManagement'

// Sales Management
import SalesOrders from '../pages/sales/SalesOrders'
import Quotations from '../pages/sales/Quotations'
import SalesPipeline from '../pages/sales/SalesPipeline'

// Training & Development
import TrainingPrograms from '../pages/training/TrainingPrograms'
import Certifications from '../pages/training/Certifications'

// Performance Management
import PerformanceReviews from '../pages/performance/PerformanceReviews'

// Recruitment
import JobPostings from '../pages/recruitment/JobPostings'

// Product Management
import ProductCatalog from '../pages/products/ProductCatalog'

// Calendar & Knowledge Base
import Calendar from '../pages/calendar/Calendar'
import KnowledgeBase from '../pages/knowledge/KnowledgeBase'

// Payroll & Compliance
import PayrollManagement from '../pages/payroll/PayrollManagement'
import RiskManagement from '../pages/compliance/RiskManagement'

// Quality & Analytics
import QualityManagement from '../pages/quality/QualityManagement'
import BusinessIntelligence from '../pages/analytics/BusinessIntelligence'

// Vendor Management
import VendorManagement from '../pages/vendors/VendorManagement'

interface SuperAdminRoutesProps {
  language: 'ar' | 'en'
}

export default function SuperAdminRoutes({ language }: SuperAdminRoutesProps) {
  return (
    <Routes>
      {/* Dashboard - Root and /admin/dashboard */}
      <Route path="/" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <SuperAdminDashboard language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/dashboard" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <SuperAdminDashboard language={language} />
        </RoleBasedRoute>
      } />

      {/* Core Management */}
      <Route path="/admin/employees" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <Employees language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/departments" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <Departments language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/reports" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <Reports language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/settings" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <Settings language={language} />
        </RoleBasedRoute>
      } />

      {/* HR Management */}
      <Route path="/admin/hr/leave" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <LeaveManagement language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/hr/attendance" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <Attendance language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/hr/performance" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <Performance language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/hr/payroll" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <Payroll language={language} />
        </RoleBasedRoute>
      } />

      {/* Sales Management */}
      <Route path="/admin/sales/orders" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <SalesOrders language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/sales/quotations" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <Quotations language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/sales/pipeline" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <SalesPipeline language={language} />
        </RoleBasedRoute>
      } />

      {/* Personal Features */}
      <Route path="/admin/profile" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <PersonalProfile language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/messages" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <PersonalMessages language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/calendar" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <PersonalCalendar language={language} />
        </RoleBasedRoute>
      } />

      {/* Communication */}
      <Route path="/admin/communication/announcements" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <Announcements language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/communication/documents" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <Documents language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/communication/meetings" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <Meetings language={language} />
        </RoleBasedRoute>
      } />

      {/* System Administration */}
      <Route path="/admin/users" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <UserManagement language={language} />
        </RoleBasedRoute>
      } />

      {/* Vendor Management */}
      <Route path="/admin/vendors" element={
        <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
          <VendorManagement language={language} />
        </RoleBasedRoute>
      } />

      {/* Unauthorized Access */}
      <Route path="/admin/unauthorized" element={<Unauthorized language={language} />) />

      {/* Catch all - redirect to admin dashboard */}
      <Route path="*" element={<Navigate to="/admin/dashboard" replace />) />
    </Routes>
  )
}
