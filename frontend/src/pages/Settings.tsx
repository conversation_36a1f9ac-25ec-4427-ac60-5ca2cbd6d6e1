import { useState } from 'react'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Settings as SettingsIcon,
  User,
  Bell,
  Shield,
  Globe,
  Palette,
  Database,
  Mail,
  Phone,
  Lock,
  Save,
  Eye,
  EyeOff
} from 'lucide-react'

interface SettingsProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    settings: 'الإعدادات',
    profile: 'الملف الشخصي',
    notifications: 'الإشعارات',
    security: 'الأمان',
    appearance: 'المظهر',
    system: 'النظام',
    saveChanges: 'حفظ التغييرات',
    cancel: 'إلغاء',
    // Profile
    personalInfo: 'المعلومات الشخصية',
    firstName: 'الاسم الأول',
    lastName: 'اسم العائلة',
    email: 'الب<PERSON>يد الإلكتروني',
    phone: 'رقم الهاتف',
    position: 'المنصب',
    department: 'القسم',
    // Notifications
    notificationSettings: 'إعدادات الإشعارات',
    emailNotifications: 'إشعارات البريد الإلكتروني',
    pushNotifications: 'الإشعارات المنبثقة',
    smsNotifications: 'إشعارات الرسائل النصية',
    weeklyReports: 'التقارير الأسبوعية',
    systemAlerts: 'تنبيهات النظام',
    // Security
    securitySettings: 'إعدادات الأمان',
    changePassword: 'تغيير كلمة المرور',
    currentPassword: 'كلمة المرور الحالية',
    newPassword: 'كلمة المرور الجديدة',
    confirmPassword: 'تأكيد كلمة المرور',
    twoFactorAuth: 'المصادقة الثنائية',
    loginHistory: 'سجل تسجيل الدخول',
    // Appearance
    appearanceSettings: 'إعدادات المظهر',
    theme: 'السمة',
    language: 'اللغة',
    fontSize: 'حجم الخط',
    lightMode: 'الوضع الفاتح',
    darkMode: 'الوضع الداكن',
    autoMode: 'تلقائي',
    // System
    systemSettings: 'إعدادات النظام',
    dataBackup: 'نسخ احتياطي للبيانات',
    exportData: 'تصدير البيانات',
    importData: 'استيراد البيانات',
    systemLogs: 'سجلات النظام',
    manageSettings: 'إدارة إعدادات النظام والحساب'
  },
  en: {
    settings: 'Settings',
    profile: 'Profile',
    notifications: 'Notifications',
    security: 'Security',
    appearance: 'Appearance',
    system: 'System',
    saveChanges: 'Save Changes',
    cancel: 'Cancel',
    // Profile
    personalInfo: 'Personal Information',
    firstName: 'First Name',
    lastName: 'Last Name',
    email: 'Email',
    phone: 'Phone',
    position: 'Position',
    department: 'Department',
    // Notifications
    notificationSettings: 'Notification Settings',
    emailNotifications: 'Email Notifications',
    pushNotifications: 'Push Notifications',
    smsNotifications: 'SMS Notifications',
    weeklyReports: 'Weekly Reports',
    systemAlerts: 'System Alerts',
    // Security
    securitySettings: 'Security Settings',
    changePassword: 'Change Password',
    currentPassword: 'Current Password',
    newPassword: 'New Password',
    confirmPassword: 'Confirm Password',
    twoFactorAuth: 'Two-Factor Authentication',
    loginHistory: 'Login History',
    // Appearance
    appearanceSettings: 'Appearance Settings',
    theme: 'Theme',
    language: 'Language',
    fontSize: 'Font Size',
    lightMode: 'Light Mode',
    darkMode: 'Dark Mode',
    autoMode: 'Auto',
    // System
    systemSettings: 'System Settings',
    dataBackup: 'Data Backup',
    exportData: 'Export Data',
    importData: 'Import Data',
    systemLogs: 'System Logs',
    manageSettings: 'Manage system and account settings'
  }
}

export default function Settings({ language }: SettingsProps) {
  const [activeTab, setActiveTab] = useState('profile')
  const [showPassword, setShowPassword] = useState(false)
  const [formData, setFormData] = useState({
    firstName: 'أحمد',
    lastName: 'حسن',
    email: '<EMAIL>',
    phone: '+966 50 123 4567',
    position: 'مدير الموارد البشرية',
    department: 'الموارد البشرية'
  })

  const t = translations[language]

  const tabs = [
    { id: 'profile', name: t.profile, icon: User },
    { id: 'notifications', name: t.notifications, icon: Bell },
    { id: 'security', name: t.security, icon: Shield },
    { id: 'appearance', name: t.appearance, icon: Palette },
    { id: 'system', name: t.system, icon: Database }
  ]

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const renderProfileSettings = () => (
    <Card className="modern-card border-0">
      <CardHeader>
        <CardTitle className="text-white text-xl">{t.personalInfo}</CardTitle>
        <CardDescription className="text-white/70">
          {language === 'ar'
            ? 'تحديث معلوماتك الشخصية'
            : 'Update your personal information'
          }
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="firstName" className="text-white/90 font-medium">{t.firstName}</Label>
            <Input
              id="firstName"
              value={formData.firstName}
              onChange={(e) => handleInputChange('firstName', e.target.value)}
              className="glass text-white placeholder-white/60 border-white/30 focus:border-white/50 mt-2"
            />
          </div>
          <div>
            <Label htmlFor="lastName" className="text-white/90 font-medium">{t.lastName}</Label>
            <Input
              id="lastName"
              value={formData.lastName}
              onChange={(e) => handleInputChange('lastName', e.target.value)}
              className="glass text-white placeholder-white/60 border-white/30 focus:border-white/50 mt-2"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="email" className="text-white/90 font-medium">{t.email}</Label>
          <div className="relative mt-2">
            <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 h-5 w-5" />
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              className="glass text-white placeholder-white/60 border-white/30 focus:border-white/50 pl-12"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="phone" className="text-white/90 font-medium">{t.phone}</Label>
          <div className="relative mt-2">
            <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 h-5 w-5" />
            <Input
              id="phone"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              className="glass text-white placeholder-white/60 border-white/30 focus:border-white/50 pl-12"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="position" className="text-white/90 font-medium">{t.position}</Label>
            <Input
              id="position"
              value={formData.position}
              onChange={(e) => handleInputChange('position', e.target.value)}
              className="glass text-white placeholder-white/60 border-white/30 focus:border-white/50 mt-2"
            />
          </div>
          <div>
            <Label htmlFor="department" className="text-white/90 font-medium">{t.department}</Label>
            <Input
              id="department"
              value={formData.department}
              onChange={(e) => handleInputChange('department', e.target.value)}
              className="glass text-white placeholder-white/60 border-white/30 focus:border-white/50 mt-2"
            />
          </div>
        </div>

        <div className="flex gap-3 pt-6">
          <Button className="gradient-bg-blue hover:scale-105 transform transition-all duration-300 glow-hover text-white font-semibold px-6 py-3">
            <Save className="h-5 w-5 mr-2" />
            {t.saveChanges}
          </Button>
          <Button className="glass text-white border-white/30 hover:bg-white/20 px-6 py-3">
            {t.cancel}
          </Button>
        </div>
      </CardContent>
    </Card>
  )

  const renderNotificationSettings = () => (
    <Card>
      <CardHeader>
        <CardTitle>{t.notificationSettings}</CardTitle>
        <CardDescription>
          {language === 'ar'
            ? 'إدارة تفضيلات الإشعارات'
            : 'Manage your notification preferences'
          }
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {[
          { key: 'email', label: t.emailNotifications, description: 'تلقي الإشعارات عبر البريد الإلكتروني' },
          { key: 'push', label: t.pushNotifications, description: 'إشعارات فورية في المتصفح' },
          { key: 'sms', label: t.smsNotifications, description: 'إشعارات عبر الرسائل النصية' },
          { key: 'reports', label: t.weeklyReports, description: 'تقارير أسبوعية عن النشاط' },
          { key: 'alerts', label: t.systemAlerts, description: 'تنبيهات النظام المهمة' }
        ].map((item) => (
          <div key={item.key} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div>
              <h3 className="font-medium text-gray-900 dark:text-white">{item.label}</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {language === 'ar' ? item.description : `Receive ${item.label.toLowerCase()}`}
              </p>
            </div>
            <Button variant="outline" size="sm">
              {language === 'ar' ? 'تفعيل' : 'Enable'}
            </Button>
          </div>
        ))}
      </CardContent>
    </Card>
  )

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>{t.changePassword}</CardTitle>
          <CardDescription>
            {language === 'ar'
              ? 'تحديث كلمة المرور لحسابك'
              : 'Update your account password'
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="currentPassword">{t.currentPassword}</Label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                id="currentPassword"
                type={showPassword ? "text" : "password"}
                className="pl-10 pr-10"
              />
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 transform -translate-y-1/2"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />)
              </Button>
            </div>
          </div>

          <div>
            <Label htmlFor="newPassword">{t.newPassword}</Label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                id="newPassword"
                type="password"
                className="pl-10"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="confirmPassword">{t.confirmPassword}</Label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                id="confirmPassword"
                type="password"
                className="pl-10"
              />
            </div>
          </div>

          <Button className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700">
            {t.saveChanges}
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>{t.twoFactorAuth}</CardTitle>
          <CardDescription>
            {language === 'ar'
              ? 'تعزيز أمان حسابك بالمصادقة الثنائية'
              : 'Enhance your account security with two-factor authentication'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button variant="outline">
            {language === 'ar' ? 'تفعيل المصادقة الثنائية' : 'Enable Two-Factor Authentication'}
          </Button>
        </CardContent>
      </Card>
    </div>
  )

  const renderAppearanceSettings = () => (
    <Card>
      <CardHeader>
        <CardTitle>{t.appearanceSettings}</CardTitle>
        <CardDescription>
          {language === 'ar'
            ? 'تخصيص مظهر التطبيق'
            : 'Customize the application appearance'
          }
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <Label>{t.theme}</Label>
          <div className="grid grid-cols-3 gap-4 mt-2">
            {[
              { key: 'light', label: t.lightMode },
              { key: 'dark', label: t.darkMode },
              { key: 'auto', label: t.autoMode }
            ].map((theme) => (
              <div key={theme.key} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800">
                <div className="text-center">
                  <div className={`w-12 h-8 mx-auto mb-2 rounded ${
                    theme.key === 'light' ? 'bg-white border' :
                    theme.key === 'dark' ? 'bg-gray-900' :
                    'bg-gradient-to-r from-white to-gray-900'
                  }`}></div>
                  <p className="text-sm font-medium">{theme.label}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div>
          <Label>{t.language}</Label>
          <div className="grid grid-cols-2 gap-4 mt-2">
            <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800">
              <div className="flex items-center gap-3">
                <Globe className="h-5 w-5" />
                <span>العربية</span>
              </div>
            </div>
            <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800">
              <div className="flex items-center gap-3">
                <Globe className="h-5 w-5" />
                <span>English</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  const renderSystemSettings = () => (
    <Card>
      <CardHeader>
        <CardTitle>{t.systemSettings}</CardTitle>
        <CardDescription>
          {language === 'ar'
            ? 'إدارة إعدادات النظام والبيانات'
            : 'Manage system settings and data'
          }
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {[
          { key: 'backup', label: t.dataBackup, description: 'إنشاء نسخة احتياطية من البيانات' },
          { key: 'export', label: t.exportData, description: 'تصدير البيانات بصيغة CSV أو Excel' },
          { key: 'import', label: t.importData, description: 'استيراد البيانات من ملف خارجي' },
          { key: 'logs', label: t.systemLogs, description: 'عرض سجلات النظام والأخطاء' }
        ].map((item) => (
          <div key={item.key} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div>
              <h3 className="font-medium text-gray-900 dark:text-white">{item.label}</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {language === 'ar' ? item.description : `Manage ${item.label.toLowerCase()}`}
              </p>
            </div>
            <Button variant="outline" size="sm">
              {language === 'ar' ? 'إدارة' : 'Manage'}
            </Button>
          </div>
        ))}
      </CardContent>
    </Card>
  )

  const renderContent = () => {
    switch (activeTab) {
      case 'profile':
        return renderProfileSettings()
      case 'notifications':
        return renderNotificationSettings()
      case 'security':
        return renderSecuritySettings()
      case 'appearance':
        return renderAppearanceSettings()
      case 'system':
        return renderSystemSettings()
      default:
        return renderProfileSettings()
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-white drop-shadow-lg">
          {t.settings}
        </h1>
        <p className="text-white/80 mt-2 text-lg">
          {t.manageSettings}
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <Card className="modern-card border-0">
            <CardContent className="p-4">
              <nav className="space-y-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center gap-3 px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${
                      activeTab === tab.id
                        ? 'glass-card text-white glow'
                        : 'text-white/80 hover:glass hover:text-white hover:scale-105'
                    }`}
                  >
                    <tab.icon className="h-5 w-5" />
                    {tab.name}
                  </button>
                ))}
              </nav>
            </CardContent>
          </Card>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          {renderContent()}
        </div>
      </div>
    </div>
  )
}
