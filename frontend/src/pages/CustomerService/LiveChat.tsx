import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { MessageSquare, Users, Clock, Send } from 'lucide-react';

interface LiveChatProps {
  language: 'ar' | 'en';
}

const LiveChat: React.FC<LiveChatProps> = ({ language }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Live Chat Support</h1>
            <p className="text-white/70">Real-time customer support and communication</p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="glass-card border-white/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">Active Chats</CardTitle>
              <MessageSquare className="h-4 w-4 text-blue-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">12</div>
              <p className="text-xs text-white/60 mt-1">Currently active</p>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">Agents Online</CardTitle>
              <Users className="h-4 w-4 text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">5</div>
              <p className="text-xs text-white/60 mt-1">Available agents</p>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">Avg Response</CardTitle>
              <Clock className="h-4 w-4 text-yellow-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">1.2m</div>
              <p className="text-xs text-white/60 mt-1">Response time</p>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">Queue Length</CardTitle>
              <Send className="h-4 w-4 text-purple-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">3</div>
              <p className="text-xs text-white/60 mt-1">Waiting customers</p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Chat Sessions */}
          <div className="lg:col-span-2">
            <Card className="glass-card border-white/20">
              <CardHeader>
                <CardTitle className="text-white">Active Chat Sessions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[1, 2, 3, 4, 5].map((chat) => (
                    <div key={chat} className="flex items-center justify-between p-4 bg-white/5 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                          <span className="text-white font-semibold">C{chat}</span>
                        </div>
                        <div>
                          <h4 className="text-white font-medium">Customer {chat}</h4>
                          <p className="text-white/60 text-sm">Last message: 2 minutes ago</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="px-2 py-1 bg-green-500/20 text-green-300 text-xs rounded">Active</span>
                        <button className="px-3 py-1 bg-blue-500/20 text-blue-300 text-xs rounded hover:bg-blue-500/30">
                          Join
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Agent Status */}
          <div>
            <Card className="glass-card border-white/20">
              <CardHeader>
                <CardTitle className="text-white">Agent Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    { name: 'Ahmed Ali', status: 'online', chats: 3 },
                    { name: 'Sara Mohamed', status: 'online', chats: 2 },
                    { name: 'Omar Hassan', status: 'busy', chats: 5 },
                    { name: 'Fatima Ahmed', status: 'online', chats: 1 },
                    { name: 'Khalid Ibrahim', status: 'away', chats: 0 },
                  ].map((agent, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${
                          agent.status === 'online' ? 'bg-green-400' :
                          agent.status === 'busy' ? 'bg-yellow-400' : 'bg-gray-400'
                        }`}></div>
                        <span className="text-white text-sm">{agent.name}</span>
                      </div>
                      <span className="text-white/60 text-xs">{agent.chats} chats</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Coming Soon Notice */}
        <Card className="glass-card border-white/20">
          <CardContent className="p-12 text-center">
            <MessageSquare className="h-16 w-16 text-white/40 mx-auto mb-4" />
            <h3 className="text-2xl font-semibold text-white mb-2">Live Chat Interface</h3>
            <p className="text-white/60 mb-4">
              Full live chat functionality with real-time messaging, file sharing, and agent management is coming soon.
            </p>
            <div className="text-sm text-white/50">
              This will include WebSocket integration for real-time communication.
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default LiveChat;
