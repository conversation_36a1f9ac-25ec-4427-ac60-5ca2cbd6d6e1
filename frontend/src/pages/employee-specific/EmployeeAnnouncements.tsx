/**
 * Employee Announcements Page - READ-ONLY Implementation
 * Restricted version for employees - can only view published announcements
 * No create, edit, delete, or export capabilities
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Megaphone,
  Calendar,
  User,
  AlertTriangle,
  Info,
  CheckCircle,
  Eye,
  Bell,
  Target,
  RefreshCw
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { announcementService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface EmployeeAnnouncementsProps {
  language: 'ar' | 'en'
}

interface Announcement {
  id: number
  title: string
  titleAr: string
  content: string
  contentAr: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  targetAudience: string
  publishDate: string
  expiryDate: string
  status: 'draft' | 'published' | 'scheduled' | 'expired'
  publishedBy: string
  views: number
  readBy: number
  isPinned: boolean
}

// Translations
const translations = {
  ar: {
    employeeAnnouncements: 'الإعلانات',
    searchPlaceholder: 'البحث في الإعلانات...',
    view: 'عرض',
    title: 'العنوان',
    priority: 'الأولوية',
    publishDate: 'تاريخ النشر',
    publishedBy: 'نشر بواسطة',
    targetAudience: 'الجمهور المستهدف',
    views: 'المشاهدات',
    low: 'منخفضة',
    medium: 'متوسطة',
    high: 'عالية',
    urgent: 'عاجلة',
    published: 'منشور',
    all: 'الكل',
    filterByPriority: 'تصفية حسب الأولوية',
    filterByAudience: 'تصفية حسب الجمهور',
    allEmployees: 'جميع الموظفين',
    department: 'القسم',
    management: 'الإدارة',
    pinned: 'مثبت'
  },
  en: {
    employeeAnnouncements: 'Announcements',
    searchPlaceholder: 'Search announcements...',
    view: 'View',
    title: 'Title',
    priority: 'Priority',
    publishDate: 'Publish Date',
    publishedBy: 'Published By',
    targetAudience: 'Target Audience',
    views: 'Views',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    urgent: 'Urgent',
    published: 'Published',
    all: 'All',
    filterByPriority: 'Filter by Priority',
    filterByAudience: 'Filter by Audience',
    allEmployees: 'All Employees',
    department: 'Department',
    management: 'Management',
    pinned: 'Pinned'
  }
}
export default function EmployeeAnnouncements({ language }: EmployeeAnnouncementsProps): React.ReactElement {
  const [showModal, setShowModal] = useState(false)

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook - READ-ONLY for employees
  const {
    items: announcements,
    selectedItem,
    loading,
    error,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    clearError
  } = useCrud<Announcement>({
    service: announcementService,
    autoLoad: true,
    pageSize: 20
  })

  // Filter announcements to only show published ones for employees
  const filteredAnnouncements = announcements.filter(announcement => 
    announcement.status === 'published' &&
    new Date(announcement.expiryDate) > new Date()
  )

  // Table columns configuration
  const columns: TableColumn<Announcement>[] = [
    {
      key: 'title',
      label: t.title,
      sortable: true,
      render: (item: Announcement) => (<div className="flex items-center space-x-3 rtl:space-x-reverse">
          <div className="flex items-center">
            <Megaphone className="h-5 w-5 text-blue-400 mr-2" />
            {item.isPinned && <Bell className="className="h-4 w-4 text-yellow-400 mr-1" />}" />}
          </div>
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.titleAr : item.title}
            </div>
            <div className="text-sm text-gray-400 line-clamp-1">
              {language === 'ar' ? item.contentAr : item.content}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'priority',
      label: t.priority,
      sortable: true,
      render: (item: Announcement) => {
        const priorityColors = {
          low: 'bg-gray-500/20 text-gray-400',
          medium: 'bg-yellow-500/20 text-yellow-400',
          high: 'bg-orange-500/20 text-orange-400',
          urgent: 'bg-red-500/20 text-red-400'
        }
        const priorityIcons = {
          low: <Info className="h-3 w-3" />,
          medium: <AlertTriangle className="h-3 w-3" />,
          high: <AlertTriangle className="h-3 w-3" />,
          urgent: <AlertTriangle className="h-3 w-3" />
        }
        return (<div className="flex items-center space-x-2 rtl:space-x-reverse">
            {priorityIcons[item.priority]}
            <Badge className={priorityColors[item.priority]}>
              {t[item.priority]}
            </Badge>
          </div>
        )
      }
    },
    {
      key: 'targetAudience',
      label: t.targetAudience,
      render: (item: Announcement) => (<div className="flex items-center space-x-2 rtl:space-x-reverse">
          <Target className="h-4 w-4 text-gray-400" />
          <span className="text-gray-300">{item.targetAudience}</span>
        </div>
      )
    },
    {
      key: 'publishDate',
      label: t.publishDate,
      sortable: true,
      render: (item: Announcement) => (<div className="flex items-center space-x-2 rtl:space-x-reverse">
          <Calendar className="h-4 w-4 text-gray-400" />
          <span className="text-gray-300">{item.publishDate}</span>
        </div>
      )
    },
    {
      key: 'publishedBy',
      label: t.publishedBy,
      render: (item: Announcement) => (<div className="flex items-center space-x-2 rtl:space-x-reverse">
          <User className="h-4 w-4 text-gray-400" />
          <span className="text-gray-300">{item.publishedBy}</span>
        </div>
      )
    },
    {
      key: 'views',
      label: t.views,
      render: (item: Announcement) => (<div className="flex items-center space-x-2 rtl:space-x-reverse">
          <Eye className="h-4 w-4 text-gray-400" />
          <span className="text-gray-300">{item.views}</span>
        </div>
      )
    }
  ]

  // Table actions configuration - RESTRICTED for employees (VIEW ONLY)
  const actions: TableAction<Announcement>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Announcement) => {
        selectItem(item)
        setShowModal(true)
      },
      variant: 'ghost'
    }
    // REMOVED: Edit, Delete, Pin, and other management actions
  ]

  // Filter options - LIMITED for employees
  const filterOptions: FilterOption[] = [
    {
      key: 'priority',
      label: t.filterByPriority,
      options: [
        { value: '', label: t.all },
        { value: 'low', label: t.low },
        { value: 'medium', label: t.medium },
        { value: 'high', label: t.high },
        { value: 'urgent', label: t.urgent }
      ]
    },
    {
      key: 'targetAudience',
      label: t.filterByAudience,
      options: [
        { value: '', label: t.all },
        { value: 'all_employees', label: t.allEmployees },
        { value: 'department', label: t.department }
        // REMOVED: Management-specific filters
      ]
    }
  ]

  // Event handlers - RESTRICTED for employees
  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  return (<div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* READ-ONLY Table for Employee Announcements */}
      <CrudTable
        title={t.employeeAnnouncements}
        data={filteredAnnouncements}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        // REMOVED: onCreate - employees cannot create announcements
        onRefresh={refresh}
        // REMOVED: onExport - employees cannot export data
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
        showCreateButton={false}
        showExportButton={false}
      />

      {/* VIEW-ONLY Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        // REMOVED: onSave - employees cannot save/edit announcements
        title={t.view}
        fields={[]} // No form fields needed for view-only
        initialData={selectedItem}
        language={language}
        loading={false}
        readOnly={true}
      />
    </div>
  )
}
