import React, { useState, useEffect } from 'react'
import { 
  <PERSON><PERSON>hart3, 
  TrendingUp, 
  TrendingDown,
  PieChart, 
  LineChart,
  Activity, 
  Users, 
  DollarSign,
  Target,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  Eye,
  Settings,
  Zap,
  Globe,
  Database,
  Clock,
  AlertTriangle,
  CheckCircle,
  ArrowUp,
  ArrowDown,
  Minus
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { Progress } from '../../components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select'

interface AdvancedAnalyticsProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    advancedAnalytics: 'التحليلات المتقدمة',
    businessIntelligence: 'ذكاء الأعمال',
    performanceAnalytics: 'تحليلات الأداء',
    userBehavior: 'سلوك المستخدمين',
    financialAnalytics: 'التحليلات المالية',
    operationalMetrics: 'المقاييس التشغيلية',
    predictiveAnalytics: 'التحليلات التنبؤية',
    realTimeData: 'البيانات الفورية',
    historicalTrends: 'الاتجاهات التاريخية',
    kpiDashboard: 'لوحة مؤشرات الأداء',
    customReports: 'التقارير المخصصة',
    dataVisualization: 'تصور البيانات',
    exportData: 'تصدير البيانات',
    filterData: 'تصفية البيانات',
    refreshData: 'تحديث البيانات',
    timeRange: 'النطاق الزمني',
    last7Days: 'آخر 7 أيام',
    last30Days: 'آخر 30 يوماً',
    last90Days: 'آخر 90 يوماً',
    lastYear: 'العام الماضي',
    customRange: 'نطاق مخصص',
    totalRevenue: 'إجمالي الإيرادات',
    totalUsers: 'إجمالي المستخدمين',
    activeUsers: 'المستخدمون النشطون',
    conversionRate: 'معدل التحويل',
    averageOrderValue: 'متوسط قيمة الطلب',
    customerLifetimeValue: 'قيمة العميل مدى الحياة',
    churnRate: 'معدل التسرب',
    retentionRate: 'معدل الاحتفاظ',
    userEngagement: 'تفاعل المستخدمين',
    sessionDuration: 'مدة الجلسة',
    pageViews: 'مشاهدات الصفحة',
    bounceRate: 'معدل الارتداد',
    topPages: 'أهم الصفحات',
    topUsers: 'أهم المستخدمين',
    trafficSources: 'مصادر الزيارات',
    deviceTypes: 'أنواع الأجهزة',
    geographicData: 'البيانات الجغرافية',
    salesForecast: 'توقعات المبيعات',
    userGrowthPrediction: 'توقع نمو المستخدمين',
    revenueProjection: 'إسقاط الإيرادات',
    riskAnalysis: 'تحليل المخاطر',
    opportunityAnalysis: 'تحليل الفرص',
    marketTrends: 'اتجاهات السوق',
    competitorAnalysis: 'تحليل المنافسين',
    customerSegmentation: 'تقسيم العملاء',
    productPerformance: 'أداء المنتجات',
    campaignEffectiveness: 'فعالية الحملات',
    high: 'عالي',
    medium: 'متوسط',
    low: 'منخفض',
    excellent: 'ممتاز',
    good: 'جيد',
    poor: 'ضعيف',
    increasing: 'متزايد',
    decreasing: 'متناقص',
    stable: 'مستقر'
  },
  en: {
    advancedAnalytics: 'Advanced Analytics',
    businessIntelligence: 'Business Intelligence',
    performanceAnalytics: 'Performance Analytics',
    userBehavior: 'User Behavior',
    financialAnalytics: 'Financial Analytics',
    operationalMetrics: 'Operational Metrics',
    predictiveAnalytics: 'Predictive Analytics',
    realTimeData: 'Real-time Data',
    historicalTrends: 'Historical Trends',
    kpiDashboard: 'KPI Dashboard',
    customReports: 'Custom Reports',
    dataVisualization: 'Data Visualization',
    exportData: 'Export Data',
    filterData: 'Filter Data',
    refreshData: 'Refresh Data',
    timeRange: 'Time Range',
    last7Days: 'Last 7 Days',
    last30Days: 'Last 30 Days',
    last90Days: 'Last 90 Days',
    lastYear: 'Last Year',
    customRange: 'Custom Range',
    totalRevenue: 'Total Revenue',
    totalUsers: 'Total Users',
    activeUsers: 'Active Users',
    conversionRate: 'Conversion Rate',
    averageOrderValue: 'Average Order Value',
    customerLifetimeValue: 'Customer Lifetime Value',
    churnRate: 'Churn Rate',
    retentionRate: 'Retention Rate',
    userEngagement: 'User Engagement',
    sessionDuration: 'Session Duration',
    pageViews: 'Page Views',
    bounceRate: 'Bounce Rate',
    topPages: 'Top Pages',
    topUsers: 'Top Users',
    trafficSources: 'Traffic Sources',
    deviceTypes: 'Device Types',
    geographicData: 'Geographic Data',
    salesForecast: 'Sales Forecast',
    userGrowthPrediction: 'User Growth Prediction',
    revenueProjection: 'Revenue Projection',
    riskAnalysis: 'Risk Analysis',
    opportunityAnalysis: 'Opportunity Analysis',
    marketTrends: 'Market Trends',
    competitorAnalysis: 'Competitor Analysis',
    customerSegmentation: 'Customer Segmentation',
    productPerformance: 'Product Performance',
    campaignEffectiveness: 'Campaign Effectiveness',
    high: 'High',
    medium: 'Medium',
    low: 'Low',
    excellent: 'Excellent',
    good: 'Good',
    poor: 'Poor',
    increasing: 'Increasing',
    decreasing: 'Decreasing',
    stable: 'Stable'
  }
}
export default function AdvancedAnalytics({ language }: AdvancedAnalyticsProps): React.ReactElement {
  const t = translations[language]
  const isRTL = language === 'ar'

  // Analytics state
  const [timeRange, setTimeRange] = useState('last30Days')
  const [analyticsData, setAnalyticsData] = useState({
    overview: {
      totalRevenue: 5850000,
      revenueGrowth: 18.3,
      totalUsers: 1247,
      userGrowth: 12.5,
      activeUsers: 142,
      activeUserGrowth: 8.7,
      conversionRate: 3.2,
      conversionGrowth: 15.4
    },
    userBehavior: {
      sessionDuration: 245,
      pageViews: 15420,
      bounceRate: 32.5,
      retentionRate: 78.9,
      churnRate: 21.1,
      userEngagement: 85.3
    },
    financial: {
      averageOrderValue: 450,
      customerLifetimeValue: 2850,
      monthlyRecurringRevenue: 125000,
      revenuePerUser: 4690
    },
    predictions: {
      nextMonthRevenue: 6200000,
      nextMonthUsers: 1380,
      riskScore: 15,
      opportunityScore: 85
    },
    topMetrics: [
      { name: 'Dashboard Views', value: 15420, change: 12.5, trend: 'up' },
      { name: 'User Registrations', value: 234, change: 8.3, trend: 'up' },
      { name: 'Revenue Growth', value: 18.3, change: 5.2, trend: 'up' },
      { name: 'System Performance', value: 98.5, change: -0.5, trend: 'down' },
      { name: 'Customer Satisfaction', value: 4.8, change: 0.2, trend: 'up' }
    ],
    trafficSources: [
      { source: 'Direct', percentage: 45.2, users: 564 },
      { source: 'Search Engines', percentage: 28.7, users: 358 },
      { source: 'Social Media', percentage: 15.3, users: 191 },
      { source: 'Email', percentage: 8.1, users: 101 },
      { source: 'Referrals', percentage: 2.7, users: 34 }
    ]
  })

  // Real-time updates from API
  useEffect(() => {
    const updateAnalyticsData = async () => {
      try {
        // TODO: Replace with real analytics API when available
        // For now, we'll keep the data static instead of using Math.random
        // The data should come from real analytics endpoints
      } catch (error) {
        console.error('Error updating analytics data:', error)
      }
    }

    // Update every 30 seconds with real data
    const interval = setInterval(updateAnalyticsData, 30000)
    return () => clearInterval(interval)
  }, [])

  const getTrendIcon = (trend: string): void => {
    switch (trend) {
      case 'up': return <ArrowUp className="h-4 w-4 text-green-500" />
      case 'down': return <ArrowDown className="h-4 w-4 text-red-500" />
      default: return <Minus className="h-4 w-4 text-gray-500" />
    }
  }

  const getTrendColor = (trend: string): void => {
    switch (trend) {
      case 'up': return 'text-green-500'
      case 'down': return 'text-red-500'
      default: return 'text-gray-500'
    }
  }

  return (<div className={`min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">{t.advancedAnalytics}</h1>
              <p className="text-white/70">Comprehensive business intelligence and data insights</p>
            </div>
            <div className="flex gap-3">
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-40 bg-white/10 border-white/20 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="last7Days">{t.last7Days}</SelectItem>
                  <SelectItem value="last30Days">{t.last30Days}</SelectItem>
                  <SelectItem value="last90Days">{t.last90Days}</SelectItem>
                  <SelectItem value="lastYear">{t.lastYear}</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                <RefreshCw className="h-4 w-4 mr-2" />
                {t.refreshData}
              </Button>
              <Button variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                <Download className="h-4 w-4 mr-2" />
                {t.exportData}
              </Button>
            </div>
          </div>
        </div>

        {/* Key Metrics Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalRevenue}</p>
                  <p className="text-2xl font-bold text-green-400">
                    ${analyticsData.overview.totalRevenue.toLocaleString()}
                  </p>
                  <div className="flex items-center gap-1 mt-1">
                    <ArrowUp className="h-3 w-3 text-green-500" />
                    <span className="text-green-500 text-xs">+{analyticsData.overview.revenueGrowth}%</span>
                  </div>
                </div>
                <div className="p-3 rounded-lg bg-green-500">
                  <DollarSign className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalUsers}</p>
                  <p className="text-2xl font-bold text-blue-400">{analyticsData.overview.totalUsers.toLocaleString()}</p>
                  <div className="flex items-center gap-1 mt-1">
                    <ArrowUp className="h-3 w-3 text-green-500" />
                    <span className="text-green-500 text-xs">+{analyticsData.overview.userGrowth}%</span>
                  </div>
                </div>
                <div className="p-3 rounded-lg bg-blue-500">
                  <Users className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.activeUsers}</p>
                  <p className="text-2xl font-bold text-purple-400">{analyticsData.overview.activeUsers}</p>
                  <div className="flex items-center gap-1 mt-1">
                    <ArrowUp className="h-3 w-3 text-green-500" />
                    <span className="text-green-500 text-xs">+{analyticsData.overview.activeUserGrowth}%</span>
                  </div>
                </div>
                <div className="p-3 rounded-lg bg-purple-500">
                  <Activity className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.conversionRate}</p>
                  <p className="text-2xl font-bold text-orange-400">{analyticsData.overview.conversionRate}%</p>
                  <div className="flex items-center gap-1 mt-1">
                    <ArrowUp className="h-3 w-3 text-green-500" />
                    <span className="text-green-500 text-xs">+{analyticsData.overview.conversionGrowth}%</span>
                  </div>
                </div>
                <div className="p-3 rounded-lg bg-orange-500">
                  <Target className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Analytics Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="bg-white/10 backdrop-blur-xl border-white/20">
            <TabsTrigger value="overview" className="data-[state=active]:bg-white/20 text-white">
              <BarChart3 className="h-4 w-4 mr-2" />
              {t.businessIntelligence}
            </TabsTrigger>
            <TabsTrigger value="behavior" className="data-[state=active]:bg-white/20 text-white">
              <Users className="h-4 w-4 mr-2" />
              {t.userBehavior}
            </TabsTrigger>
            <TabsTrigger value="financial" className="data-[state=active]:bg-white/20 text-white">
              <DollarSign className="h-4 w-4 mr-2" />
              {t.financialAnalytics}
            </TabsTrigger>
            <TabsTrigger value="predictive" className="data-[state=active]:bg-white/20 text-white">
              <TrendingUp className="h-4 w-4 mr-2" />
              {t.predictiveAnalytics}
            </TabsTrigger>
          </TabsList>

          {/* Business Intelligence Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Top Metrics */}
              <Card className="bg-white/10 backdrop-blur-xl border-white/20">
                <CardHeader>
                  <CardTitle className="text-white">Top Performance Metrics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analyticsData.topMetrics.map((metric, index) => (<div key={index} className="flex items-center justify-between">
                        <div>
                          <p className="text-white font-medium">{metric.name}</p>
                          <p className="text-white/70 text-sm">{metric.value.toLocaleString()}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          {getTrendIcon(metric.trend)}
                          <span className={`text-sm ${getTrendColor(metric.trend)}`}>
                            {metric.change > 0 ? '+' : ''}{metric.change}%
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Traffic Sources */}
              <Card className="bg-white/10 backdrop-blur-xl border-white/20">
                <CardHeader>
                  <CardTitle className="text-white">{t.trafficSources}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analyticsData.trafficSources.map((source, index) => (<div key={index} className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-white">{source.source}</span>
                          <span className="text-white/70">{source.percentage}%</span>
                        </div>
                        <Progress value={source.percentage} className="h-2" />
                        <p className="text-xs text-white/50">{source.users} users</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* User Behavior Tab */}
          <TabsContent value="behavior" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <Card className="bg-white/10 backdrop-blur-xl border-white/20">
                <CardHeader>
                  <CardTitle className="text-white">{t.sessionDuration}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-400 mb-2">
                      {Math.floor(analyticsData.userBehavior.sessionDuration / 60)}m {analyticsData.userBehavior.sessionDuration % 60}s
                    </div>
                    <Badge className="bg-green-500">+12.5%</Badge>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/10 backdrop-blur-xl border-white/20">
                <CardHeader>
                  <CardTitle className="text-white">{t.bounceRate}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-orange-400 mb-2">
                      {analyticsData.userBehavior.bounceRate}%
                    </div>
                    <Badge className="bg-yellow-500">-3.2%</Badge>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/10 backdrop-blur-xl border-white/20">
                <CardHeader>
                  <CardTitle className="text-white">{t.retentionRate}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-400 mb-2">
                      {analyticsData.userBehavior.retentionRate}%
                    </div>
                    <Badge className="bg-green-500">+5.8%</Badge>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Financial Analytics Tab */}
          <TabsContent value="financial" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-white/10 backdrop-blur-xl border-white/20">
                <CardHeader>
                  <CardTitle className="text-white">Financial KPIs</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-white/70">{t.averageOrderValue}</span>
                    <span className="text-white font-semibold">${analyticsData.financial.averageOrderValue}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">{t.customerLifetimeValue}</span>
                    <span className="text-white font-semibold">${analyticsData.financial.customerLifetimeValue}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Monthly Recurring Revenue</span>
                    <span className="text-white font-semibold">${analyticsData.financial.monthlyRecurringRevenue.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Revenue Per User</span>
                    <span className="text-white font-semibold">${analyticsData.financial.revenuePerUser}</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/10 backdrop-blur-xl border-white/20">
                <CardHeader>
                  <CardTitle className="text-white">Revenue Trends</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-400 mb-2">
                        ${analyticsData.overview.totalRevenue.toLocaleString()}
                      </div>
                      <p className="text-white/70">Current Month Revenue</p>
                      <div className="flex items-center justify-center gap-1 mt-2">
                        <ArrowUp className="h-4 w-4 text-green-500" />
                        <span className="text-green-500">+{analyticsData.overview.revenueGrowth}% from last month</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Predictive Analytics Tab */}
          <TabsContent value="predictive" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-white/10 backdrop-blur-xl border-white/20">
                <CardHeader>
                  <CardTitle className="text-white">{t.salesForecast}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-white/70">Next Month Revenue</span>
                      <span className="text-white font-semibold">${analyticsData.predictions.nextMonthRevenue.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">Predicted Users</span>
                      <span className="text-white font-semibold">{analyticsData.predictions.nextMonthUsers.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">Confidence Level</span>
                      <Badge className="bg-green-500">85%</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/10 backdrop-blur-xl border-white/20">
                <CardHeader>
                  <CardTitle className="text-white">{t.riskAnalysis}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-white/70">Risk Score</span>
                      <Badge className="bg-green-500">{analyticsData.predictions.riskScore}% Low</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">Opportunity Score</span>
                      <Badge className="bg-blue-500">{analyticsData.predictions.opportunityScore}% High</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">Market Outlook</span>
                      <Badge className="bg-green-500">{t.excellent}</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
