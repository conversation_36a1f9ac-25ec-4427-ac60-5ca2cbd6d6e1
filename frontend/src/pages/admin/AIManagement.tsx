import React, { useState, useEffect } from 'react'
import { toast } from 'react-hot-toast'
import {
  Brain,
  Zap,
  Settings,
  Activity,
  BarChart3,
  TrendingUp,
  Cpu,
  Database,
  Globe,
  Key,
  Eye,
  Play,
  Pause,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  Users,
  MessageSquare,
  FileText,
  Image,
  Mic,
  Video,
  Code,
  Search,
  Filter,
  Download,
  Upload,
  Volume2
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { Progress } from '../../components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs'
import { Switch } from '../../components/ui/switch'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'

interface AIManagementProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    aiManagement: 'إدارة الذكاء الاصطناعي',
    aiOverview: 'نظرة عامة على الذكاء الاصطناعي',
    modelManagement: 'إدارة النماذج',
    usageAnalytics: 'تحليلات الاستخدام',
    apiConfiguration: 'تكوين API',
    costManagement: 'إدارة التكلفة',
    performanceMetrics: 'مقاييس الأداء',
    aiFeatures: 'ميزات الذكاء الاصطناعي',
    activeModels: 'النماذج النشطة',
    totalRequests: 'إجمالي الطلبات',
    successRate: 'معدل النجاح',
    averageLatency: 'متوسط زمن الاستجابة',
    tokensUsed: 'الرموز المستخدمة',
    costThisMonth: 'التكلفة هذا الشهر',
    dailyUsage: 'الاستخدام اليومي',
    monthlyLimit: 'الحد الشهري',
    chatAssistant: 'مساعد المحادثة',
    textGeneration: 'توليد النصوص',
    imageGeneration: 'توليد الصور',
    speechToText: 'تحويل الكلام إلى نص',
    textToSpeech: 'تحويل النص إلى كلام',
    documentAnalysis: 'تحليل المستندات',
    codeGeneration: 'توليد الكود',
    dataAnalysis: 'تحليل البيانات',
    predictiveAnalytics: 'التحليلات التنبؤية',
    sentimentAnalysis: 'تحليل المشاعر',
    languageTranslation: 'ترجمة اللغات',
    contentModeration: 'إشراف المحتوى',
    enabled: 'مفعل',
    disabled: 'معطل',
    configure: 'تكوين',
    monitor: 'مراقبة',
    optimize: 'تحسين',
    upgrade: 'ترقية',
    downgrade: 'تخفيض',
    pause: 'إيقاف مؤقت',
    resume: 'استئناف',
    reset: 'إعادة تعيين',
    export: 'تصدير',
    import: 'استيراد',
    backup: 'نسخ احتياطي',
    restore: 'استعادة',
    apiKey: 'مفتاح API',
    endpoint: 'نقطة النهاية',
    model: 'النموذج',
    temperature: 'درجة الحرارة',
    maxTokens: 'الحد الأقصى للرموز',
    topP: 'Top P',
    frequencyPenalty: 'عقوبة التكرار',
    presencePenalty: 'عقوبة الحضور',
    timeout: 'انتهاء المهلة',
    retries: 'المحاولات',
    rateLimit: 'حد المعدل',
    concurrent: 'متزامن',
    queue: 'طابور',
    cache: 'ذاكرة التخزين المؤقت',
    logs: 'السجلات',
    errors: 'الأخطاء',
    warnings: 'التحذيرات',
    info: 'معلومات',
    debug: 'تصحيح الأخطاء',
    high: 'عالي',
    medium: 'متوسط',
    low: 'منخفض',
    excellent: 'ممتاز',
    good: 'جيد',
    poor: 'ضعيف',
    healthy: 'سليم',
    warning: 'تحذير',
    critical: 'حرج'
  },
  en: {
    aiManagement: 'AI Management',
    aiOverview: 'AI Overview',
    modelManagement: 'Model Management',
    usageAnalytics: 'Usage Analytics',
    apiConfiguration: 'API Configuration',
    costManagement: 'Cost Management',
    performanceMetrics: 'Performance Metrics',
    aiFeatures: 'AI Features',
    activeModels: 'Active Models',
    totalRequests: 'Total Requests',
    successRate: 'Success Rate',
    averageLatency: 'Average Latency',
    tokensUsed: 'Tokens Used',
    costThisMonth: 'Cost This Month',
    dailyUsage: 'Daily Usage',
    monthlyLimit: 'Monthly Limit',
    chatAssistant: 'Chat Assistant',
    textGeneration: 'Text Generation',
    imageGeneration: 'Image Generation',
    speechToText: 'Speech to Text',
    textToSpeech: 'Text to Speech',
    documentAnalysis: 'Document Analysis',
    codeGeneration: 'Code Generation',
    dataAnalysis: 'Data Analysis',
    predictiveAnalytics: 'Predictive Analytics',
    sentimentAnalysis: 'Sentiment Analysis',
    languageTranslation: 'Language Translation',
    contentModeration: 'Content Moderation',
    enabled: 'Enabled',
    disabled: 'Disabled',
    configure: 'Configure',
    monitor: 'Monitor',
    optimize: 'Optimize',
    upgrade: 'Upgrade',
    downgrade: 'Downgrade',
    pause: 'Pause',
    resume: 'Resume',
    reset: 'Reset',
    export: 'Export',
    import: 'Import',
    backup: 'Backup',
    restore: 'Restore',
    apiKey: 'API Key',
    endpoint: 'Endpoint',
    model: 'Model',
    temperature: 'Temperature',
    maxTokens: 'Max Tokens',
    topP: 'Top P',
    frequencyPenalty: 'Frequency Penalty',
    presencePenalty: 'Presence Penalty',
    timeout: 'Timeout',
    retries: 'Retries',
    rateLimit: 'Rate Limit',
    concurrent: 'Concurrent',
    queue: 'Queue',
    cache: 'Cache',
    logs: 'Logs',
    errors: 'Errors',
    warnings: 'Warnings',
    info: 'Info',
    debug: 'Debug',
    high: 'High',
    medium: 'Medium',
    low: 'Low',
    excellent: 'Excellent',
    good: 'Good',
    poor: 'Poor',
    healthy: 'Healthy',
    warning: 'Warning',
    critical: 'Critical'
  }
}
export default function AIManagement({ language }: AIManagementProps): React.ReactElement {
  const t = translations[language]
  const isRTL = language === 'ar'

  // AI metrics state
  const [aiMetrics, setAiMetrics] = useState({
    activeModels: 8,
    totalRequests: 45672,
    successRate: 98.5,
    averageLatency: 245,
    tokensUsed: 2456789,
    costThisMonth: 1247.50,
    dailyUsage: 15420,
    monthlyLimit: 500000,
    features: {
      chatAssistant: { enabled: true, usage: 85, cost: 245.30 },
      textGeneration: { enabled: true, usage: 72, cost: 189.20 },
      imageGeneration: { enabled: false, usage: 0, cost: 0 },
      speechToText: { enabled: true, usage: 45, cost: 67.80 },
      textToSpeech: { enabled: true, usage: 38, cost: 45.90 },
      documentAnalysis: { enabled: true, usage: 92, cost: 312.40 },
      codeGeneration: { enabled: true, usage: 67, cost: 156.70 },
      dataAnalysis: { enabled: true, usage: 78, cost: 198.60 },
      predictiveAnalytics: { enabled: false, usage: 0, cost: 0 },
      sentimentAnalysis: { enabled: true, usage: 56, cost: 89.30 },
      languageTranslation: { enabled: true, usage: 34, cost: 78.20 },
      contentModeration: { enabled: true, usage: 89, cost: 134.50 }
    },
    models: [
      { name: 'GPT-4', status: 'healthy', usage: 85, latency: 245, cost: 456.30 },
      { name: 'GPT-3.5 Turbo', status: 'healthy', usage: 92, latency: 180, cost: 234.50 },
      { name: 'Claude-3', status: 'healthy', usage: 67, latency: 220, cost: 189.70 },
      { name: 'Gemini Pro', status: 'warning', usage: 45, latency: 320, cost: 123.40 },
      { name: 'DALL-E 3', status: 'disabled', usage: 0, latency: 0, cost: 0 },
      { name: 'Whisper', status: 'healthy', usage: 78, latency: 150, cost: 89.60 },
      { name: 'Text-to-Speech', status: 'healthy', usage: 56, latency: 120, cost: 67.80 },
      { name: 'Embedding Model', status: 'healthy', usage: 89, latency: 95, cost: 45.20 }
    ]
  })

  // Real-time updates from actual AI usage APIs
  useEffect(() => {
    const fetchRealAIMetrics = async () => {
      try {
        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'}/admin/ai-metrics/`, {
          credentials: 'include',
          headers: { 'Content-Type': 'application/json' }
        })

        if (response.ok) {
          const realMetrics = await response.json()
          setAiMetrics(prev => ({
            ...prev,
            ...realMetrics
          }))
        }
      } catch (error) {
        console.error('Error fetching AI metrics:', error)
        // Keep current values if API fails
      }
    }

    // Fetch real data initially
    fetchRealAIMetrics()

    // Update every 30 seconds with real data
    const interval = setInterval(fetchRealAIMetrics, 30000)
    return () => clearInterval(interval)
  }, [])

  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'healthy': return 'text-green-500 bg-green-500/20'
      case 'warning': return 'text-yellow-500 bg-yellow-500/20'
      case 'critical': return 'text-red-500 bg-red-500/20'
      case 'disabled': return 'text-gray-500 bg-gray-500/20'
      default: return 'text-gray-500 bg-gray-500/20'
    }
  }

  const toggleFeature = (featureName: string): void => {
    setAiMetrics(prev => ({
      ...prev,
      features: {
        ...prev.features,
        [featureName]: {
          ...prev.features[featureName as keyof typeof prev.features],
          enabled: !prev.features[featureName as keyof typeof prev.features].enabled
        }
      }
    }))

    const isEnabled = !aiMetrics.features[featureName as keyof typeof aiMetrics.features].enabled
    toast.success(language === 'ar'
        ? `تم ${isEnabled ? 'تفعيل' : 'إلغاء تفعيل'} الميزة بنجاح`
        : `Feature ${isEnabled ? 'enabled' : 'disabled'} successfully`)
  }

  // AI Settings handler
  const handleAISettings = (): void => {
    toast.success(language === 'ar'
        ? 'فتح إعدادات الذكاء الاصطناعي'
        : 'Opening AI settings')
  }

  // Configure model handler
  const handleConfigureModel = (modelName: string): void => {
    toast.success(language === 'ar'
        ? `تكوين النموذج ${modelName}`
        : `Configuring model ${modelName}`)
  }

  // Configure feature handler
  const handleConfigureFeature = (featureName: string): void => {
    toast.success(language === 'ar'
        ? `تكوين الميزة ${featureName}`
        : `Configuring feature ${featureName}`)
  }

  return (
    <div className={`min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">{t.aiManagement}</h1>
              <p className="text-white/70">Comprehensive AI system management and monitoring</p>
            </div>
            <div className="flex gap-3">
              <Button
                variant="outline"
                className="bg-white/10 border-white/20 text-white hover:bg-white/20"
                onClick={handleAISettings}
              >
                <Settings className="h-4 w-4 mr-2" />
                AI Settings
              </Button>
            </div>
          </div>
        </div>

        {/* AI Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.activeModels}</p>
                  <p className="text-2xl font-bold text-blue-400">{aiMetrics.activeModels}</p>
                  <p className="text-xs text-white/50">Running</p>
                </div>
                <div className="p-3 rounded-lg bg-blue-500">
                  <Brain className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalRequests}</p>
                  <p className="text-2xl font-bold text-green-400">{aiMetrics.totalRequests.toLocaleString()}</p>
                  <p className="text-xs text-white/50">This month</p>
                </div>
                <div className="p-3 rounded-lg bg-green-500">
                  <Activity className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.successRate}</p>
                  <p className="text-2xl font-bold text-green-400">{aiMetrics.successRate}%</p>
                  <p className="text-xs text-white/50">Excellent</p>
                </div>
                <div className="p-3 rounded-lg bg-green-500">
                  <CheckCircle className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.costThisMonth}</p>
                  <p className="text-2xl font-bold text-yellow-400">${aiMetrics.costThisMonth}</p>
                  <p className="text-xs text-white/50">Budget: $2000</p>
                </div>
                <div className="p-3 rounded-lg bg-yellow-500">
                  <DollarSign className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="bg-white/10 backdrop-blur-xl border-white/20">
            <TabsTrigger value="overview" className="data-[state=active]:bg-white/20 text-white">
              <BarChart3 className="h-4 w-4 mr-2" />
              {t.aiOverview}
            </TabsTrigger>
            <TabsTrigger value="models" className="data-[state=active]:bg-white/20 text-white">
              <Brain className="h-4 w-4 mr-2" />
              {t.modelManagement}
            </TabsTrigger>
            <TabsTrigger value="features" className="data-[state=active]:bg-white/20 text-white">
              <Zap className="h-4 w-4 mr-2" />
              {t.aiFeatures}
            </TabsTrigger>
            <TabsTrigger value="analytics" className="data-[state=active]:bg-white/20 text-white">
              <TrendingUp className="h-4 w-4 mr-2" />
              {t.usageAnalytics}
            </TabsTrigger>
          </TabsList>

          {/* AI Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Performance Metrics */}
              <Card className="bg-white/10 backdrop-blur-xl border-white/20">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    {t.performanceMetrics}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm text-white/70 mb-2">
                      <span>{t.averageLatency}</span>
                      <span>{aiMetrics.averageLatency}ms</span>
                    </div>
                    <Progress value={Math.min(100, (aiMetrics.averageLatency / 500) * 100)} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm text-white/70 mb-2">
                      <span>{t.successRate}</span>
                      <span>{aiMetrics.successRate}%</span>
                    </div>
                    <Progress value={aiMetrics.successRate} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm text-white/70 mb-2">
                      <span>{t.dailyUsage}</span>
                      <span>{((aiMetrics.dailyUsage / aiMetrics.monthlyLimit) * 100).toFixed(1)}%</span>
                    </div>
                    <Progress value={(aiMetrics.dailyUsage / aiMetrics.monthlyLimit) * 100} className="h-2" />
                  </div>
                </CardContent>
              </Card>

              {/* Cost Management */}
              <Card className="bg-white/10 backdrop-blur-xl border-white/20">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    {t.costManagement}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-white/70">{t.costThisMonth}</span>
                    <span className="text-white font-semibold">${aiMetrics.costThisMonth}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">{t.tokensUsed}</span>
                    <span className="text-white font-semibold">{aiMetrics.tokensUsed.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Budget Remaining</span>
                    <span className="text-white font-semibold">${(2000 - aiMetrics.costThisMonth).toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Projected Monthly</span>
                    <span className="text-white font-semibold">${(aiMetrics.costThisMonth * 1.2).toFixed(2)}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Model Management Tab */}
          <TabsContent value="models" className="space-y-6">
            <Card className="bg-white/10 backdrop-blur-xl border-white/20">
              <CardHeader>
                <CardTitle className="text-white">{t.modelManagement}</CardTitle>
                <CardDescription className="text-white/70">
                  Manage and monitor AI models
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {aiMetrics.models.map((model, index) => (<div key={index} className="flex items-center justify-between p-4 rounded-lg bg-white/5 border border-white/10">
                      <div className="flex items-center gap-4">
                        <div className={`p-2 rounded-lg ${getStatusColor(model.status)}`}>
                          <Brain className="h-4 w-4" />
                        </div>
                        <div>
                          <h4 className="text-white font-medium">{model.name}</h4>
                          <div className="flex items-center gap-4 mt-1 text-xs text-white/50">
                            <span>Usage: {model.usage}%</span>
                            <span>Latency: {model.latency}ms</span>
                            <span>Cost: ${model.cost}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={getStatusColor(model.status)}>
                          {t[model.status as keyof typeof t]}
                        </Badge>
                        <Button
                          size="sm"
                          variant="outline"
                          className="bg-white/10 border-white/20 text-white hover:bg-white/20"
                          onClick={() => handleConfigureModel(model.name)}
                        >
                          {t.configure}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* AI Features Tab */}
          <TabsContent value="features" className="space-y-6">
            <Card className="bg-white/10 backdrop-blur-xl border-white/20">
              <CardHeader>
                <CardTitle className="text-white">{t.aiFeatures}</CardTitle>
                <CardDescription className="text-white/70">
                  Enable or disable AI features
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(aiMetrics.features).map(([key, feature]) => (<div key={key} className="flex items-center justify-between p-4 rounded-lg bg-white/5 border border-white/10">
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-lg ${feature.enabled ? 'bg-green-500' : 'bg-gray-500'}`}>
                          {key === 'chatAssistant' && <MessageSquare className="h-4 w-4 text-white" />}
                          {key === 'textGeneration' && <FileText className="h-4 w-4 text-white" />}
                          {key === 'imageGeneration' && <Image className="h-4 w-4 text-white" />}
                          {key === 'speechToText' && <Mic className="h-4 w-4 text-white" />}
                          {key === 'textToSpeech' && <Volume2 className="h-4 w-4 text-white" />}
                          {key === 'documentAnalysis' && <FileText className="h-4 w-4 text-white" />}
                          {key === 'codeGeneration' && <Code className="h-4 w-4 text-white" />}
                          {key === 'dataAnalysis' && <BarChart3 className="h-4 w-4 text-white" />}
                          {key === 'predictiveAnalytics' && <TrendingUp className="h-4 w-4 text-white" />}
                          {key === 'sentimentAnalysis' && <Brain className="h-4 w-4 text-white" />}
                          {key === 'languageTranslation' && <Globe className="h-4 w-4 text-white" />}
                          {key === 'contentModeration' && <Eye className="h-4 w-4 text-white" />}
                        </div>
                        <div>
                          <h4 className="text-white font-medium">{t[key as keyof typeof t]}</h4>
                          <div className="text-xs text-white/50">
                            Usage: {feature.usage}% | Cost: ${feature.cost}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={feature.enabled}
                          onCheckedChange={() => toggleFeature(key)}
                        />
                        <Button
                          size="sm"
                          variant="outline"
                          className="bg-white/10 border-white/20 text-white hover:bg-white/20"
                          onClick={() => handleConfigureFeature(key)}
                        >
                          {t.configure}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Usage Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-white/10 backdrop-blur-xl border-white/20">
                <CardHeader>
                  <CardTitle className="text-white">Usage Trends</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-white/70">Today</span>
                      <span className="text-white font-semibold">{aiMetrics.dailyUsage.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">This Week</span>
                      <span className="text-white font-semibold">{(aiMetrics.dailyUsage * 7).toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">This Month</span>
                      <span className="text-white font-semibold">{aiMetrics.totalRequests.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">Growth Rate</span>
                      <span className="text-green-400 font-semibold">+23.5%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/10 backdrop-blur-xl border-white/20">
                <CardHeader>
                  <CardTitle className="text-white">Top Features</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(aiMetrics.features)
                      .filter(([_, feature]) => feature.enabled)
                      .sort((a, b) => b[1].usage - a[1].usage)
                      .slice(0, 5)
                      .map(([key, feature]) => (<div key={key} className="flex justify-between items-center">
                          <span className="text-white/70">{t[key as keyof typeof t]}</span>
                          <div className="flex items-center gap-2">
                            <Progress value={feature.usage} className="w-16 h-2" />
                            <span className="text-white text-sm">{feature.usage}%</span>
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
