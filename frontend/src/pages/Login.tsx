import { useState, useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Navigate } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import type { AppDispatch, RootState } from '../store'
import { loginUser, clearError } from '../store/slices/authSlice'
import {
  Eye,
  EyeOff,
  Mail,
  Lock,
  Shield,
  Users,
  DollarSign,
  Briefcase,
  Building,
  User
} from 'lucide-react'

interface LoginProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    login: 'تسجيل الدخول',
    welcome: 'مرحباً بك في نمو',
    subtitle: 'نظام إدارة المؤسسات المتكامل',
    email: 'البريد الإلكتروني',
    password: 'كلمة المرور',
    loginButton: 'تسجيل الدخول',
    forgotPassword: 'نسيت كلمة المرور؟',
    rememberMe: 'تذكرني',
    demoAccounts: 'حسابات تجريبية',
    superAdmin: 'مدير النظام الرئيسي',
    hrManager: 'مدير الموارد البشرية',
    financeManager: 'مدير المالية',
    salesManager: 'مدير المبيعات',
    departmentManager: 'مدير القسم',
    employee: 'موظف',

    loginAs: 'تسجيل الدخول كـ',
    emailPlaceholder: 'أدخل بريدك الإلكتروني',
    passwordPlaceholder: 'أدخل كلمة المرور',
    loggingIn: 'جاري تسجيل الدخول...',
    invalidCredentials: 'بيانات الدخول غير صحيحة',
    loginFailed: 'فشل في تسجيل الدخول',
    tooManyAttempts: 'محاولات كثيرة، حاول مرة أخرى لاحقاً'
  },
  en: {
    login: 'Login',
    welcome: 'Welcome to Numu',
    subtitle: 'Integrated Enterprise Management System',
    email: 'Email',
    password: 'Password',
    loginButton: 'Login',
    forgotPassword: 'Forgot Password?',
    rememberMe: 'Remember Me',
    demoAccounts: 'Demo Accounts',
    superAdmin: 'Super Administrator',
    hrManager: 'HR Manager',
    financeManager: 'Finance Manager',
    salesManager: 'Sales Manager',
    departmentManager: 'Department Manager',
    employee: 'Employee',

    loginAs: 'Login as',
    emailPlaceholder: 'Enter your email',
    passwordPlaceholder: 'Enter your password',
    loggingIn: 'Logging in...',
    invalidCredentials: 'Invalid credentials',
    loginFailed: 'Login failed',
    tooManyAttempts: 'Too many attempts, try again later'
  }
}

// Development demo accounts for testing - replace with real authentication
const demoAccounts = [
  {
    role: 'super_admin',
    email: '<EMAIL>',
    name: 'Ahmed Al-Rashid',
    nameAr: 'أحمد الراشد',
    icon: Shield,
    color: 'from-red-500 to-red-600'
  },
  {
    role: 'hr_manager',
    email: '<EMAIL>',
    name: 'Fatima Al-Zahra',
    nameAr: 'فاطمة الزهراء',
    icon: Users,
    color: 'from-blue-500 to-blue-600'
  },
  {
    role: 'finance_manager',
    email: '<EMAIL>',
    name: 'Mohammed Al-Mansouri',
    nameAr: 'محمد المنصوري',
    icon: DollarSign,
    color: 'from-green-500 to-green-600'
  },
  {
    role: 'sales_manager',
    email: '<EMAIL>',
    name: 'Omar Al-Harbi',
    nameAr: 'عمر الحربي',
    icon: Briefcase,
    color: 'from-cyan-500 to-cyan-600'
  },
  {
    role: 'department_manager',
    email: '<EMAIL>',
    name: 'Khalid Al-Otaibi',
    nameAr: 'خالد العتيبي',
    icon: Building,
    color: 'from-purple-500 to-purple-600'
  },
  {
    role: 'employee',
    email: '<EMAIL>',
    name: 'Sara Al-Ahmad',
    nameAr: 'سارة الأحمد',
    icon: User,
    color: 'from-orange-500 to-orange-600'
  }
]

export default function Login({ language }: LoginProps) {
  const dispatch = useDispatch<AppDispatch>()
  const { isAuthenticated, isLoading, error, loginAttempts } = useSelector((state: RootState) => state.auth)

  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [rememberMe, setRememberMe] = useState(false)

  const t = translations[language]
  const isRTL = language === 'ar'

  // Clear error when component mounts
  useEffect(() => {
    dispatch(clearError())
  }, [dispatch])

  // Redirect if already authenticated
  if (isAuthenticated) {
    return <Navigate to="/" replace />
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (loginAttempts >= 5) {
      return
    }

    // Clear any previous errors
    dispatch(clearError())

    // Validate form data
    if (!formData.email || !formData.password) {
      return
    }

    dispatch(loginUser(formData))
  }

  const handleDemoLogin = (email: string) => {
    setFormData({ email, password: 'password123' })
    dispatch(loginUser({ email, password: 'password123' }))
  }

  return (
    <div className={`min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Background Effects */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
      </div>

      <div className="relative z-10 w-full max-w-6xl grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Side - Branding */}
        <div className="flex flex-col justify-center space-y-8">
          <div className="text-center lg:text-right">
            <h1 className="text-5xl font-bold text-white mb-4 drop-shadow-lg animate-fade-in-up">
              {t.welcome}
            </h1>
            <p className="text-xl text-white/80 mb-8 animate-fade-in-up animation-delay-200">
              {t.subtitle}
            </p>
          </div>

        </div>

        {/* Right Side - Login Form */}
        <div className="flex flex-col justify-center animate-fade-in-up animation-delay-400">
          <Card className="glass-card border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-300">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl font-bold text-white">{t.login}</CardTitle>
              <CardDescription className="text-white/70">
                {language === 'ar' ? 'أدخل بياناتك للوصول إلى النظام' : 'Enter your credentials to access the system'}
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-6">
              {/* Error Message */}
              {error && (
                <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-3">
                  <p className="text-red-400 text-sm text-center">
                    {loginAttempts >= 5 ? t.tooManyAttempts : error}
                  </p>
                </div>
              )}

              {/* Login Form */}
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-white">{t.email}</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                    <Input
                      id="email"
                      type="email"
                      placeholder={t.emailPlaceholder}
                      value={formData.email}
                      onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                      className="pl-10 glass-input"
                      required
                      disabled={isLoading || loginAttempts >= 5}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password" className="text-white">{t.password}</Label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                    <Input
                      id="password"
                      type={showPassword ? 'text' : 'password'}
                      placeholder={t.passwordPlaceholder}
                      value={formData.password}
                      onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                      className="pl-10 pr-10 glass-input"
                      required
                      disabled={isLoading || loginAttempts >= 5}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white"
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="remember"
                      checked={rememberMe}
                      onChange={(e) => setRememberMe(e.target.checked)}
                      className="rounded border-white/30 bg-white/10"
                    />
                    <Label htmlFor="remember" className="text-white/80 text-sm">
                      {t.rememberMe}
                    </Label>
                  </div>
                  <Button variant="link" className="text-blue-400 hover:text-blue-300 p-0">
                    {t.forgotPassword}
                  </Button>
                </div>

                <Button
                  type="submit"
                  className="w-full glass-button bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold py-3 transition-all duration-300"
                  disabled={isLoading || loginAttempts >= 5 || !formData.email || !formData.password}
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      {t.loggingIn}
                    </div>
                  ) : (
                    t.loginButton
                  )}
                </Button>
              </form>

              {/* Demo Accounts */}
              <div className="space-y-4">
                <div className="text-center">
                  <p className="text-white/60 text-sm">{t.demoAccounts}</p>
                </div>

                <div className="grid grid-cols-1 gap-2">
                  {demoAccounts.map((account, index) => (
                    <Button
                      key={account.role}
                      variant="outline"
                      onClick={() => handleDemoLogin(account.email)}
                      disabled={isLoading}
                      className={`glass-button justify-start hover:border-white/40 transition-all duration-300 animate-fade-in-up`}
                      style={{ animationDelay: `${0.6 + index * 0.1}s` }}
                    >
                      <div className={`p-2 rounded-lg bg-gradient-to-r ${account.color} mr-3 transition-transform duration-300 hover:scale-110`}>
                        <account.icon className="h-4 w-4 text-white" />
                      </div>
                      <div className="text-left">
                        <div className="font-medium text-white">
                          {language === 'ar' ? account.nameAr : account.name}
                        </div>
                        <div className="text-xs text-white/60">
                          {(() => {
                            const roleKey = account.role.replace('_', '') as keyof typeof t
                            return t[roleKey] || account.role
                          })()}
                        </div>
                      </div>
                    </Button>
                  ))}
                </div>

                <div className="text-center space-y-2">
                  <p className="text-xs text-white/50">
                    {language === 'ar'
                      ? 'كلمة المرور لجميع الحسابات التجريبية: password123'
                      : 'Password for all demo accounts: password123'
                    }
                  </p>
                  <p className="text-xs text-white/40">
                    {language === 'ar'
                      ? 'انقر على أي حساب تجريبي لتسجيل الدخول التلقائي'
                      : 'Click any demo account for automatic login'
                    }
                  </p>
                </div>
              </div>

            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
