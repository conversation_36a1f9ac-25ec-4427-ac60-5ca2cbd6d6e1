import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { employeeAPI } from '@/services/employeeAPI'
import {
  Users,
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Mail,
  Phone,
  Calendar,
  Building,
  User
} from 'lucide-react'

interface EmployeesProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    employees: 'الموظفين',
    addEmployee: 'إضافة موظف',
    searchEmployees: 'البحث في الموظفين',
    filter: 'تصفية',
    totalEmployees: 'إجمالي الموظفين',
    activeEmployees: 'الموظفين النشطين',
    newHires: 'التوظيفات الجديدة',
    employeeList: 'قائمة الموظفين',
    name: 'الاسم',
    position: 'المنصب',
    department: 'القسم',
    email: 'البريد الإلكتروني',
    phone: 'الهاتف',
    hireDate: 'تاريخ التوظيف',
    status: 'الحالة',
    actions: 'الإجراءات',
    active: 'نشط',
    inactive: 'غير نشط',
    edit: 'تعديل',
    delete: 'حذف',
    view: 'عرض',
    searchPlaceholder: 'البحث بالاسم أو البريد الإلكتروني...',
    noEmployees: 'لا يوجد موظفين',
    loading: 'جاري التحميل...'
  },
  en: {
    employees: 'Employees',
    addEmployee: 'Add Employee',
    searchEmployees: 'Search Employees',
    filter: 'Filter',
    totalEmployees: 'Total Employees',
    activeEmployees: 'Active Employees',
    newHires: 'New Hires',
    employeeList: 'Employee List',
    name: 'Name',
    position: 'Position',
    department: 'Department',
    email: 'Email',
    phone: 'Phone',
    hireDate: 'Hire Date',
    status: 'Status',
    actions: 'Actions',
    active: 'Active',
    inactive: 'Inactive',
    edit: 'Edit',
    delete: 'Delete',
    view: 'View',
    searchPlaceholder: 'Search by name or email...',
    noEmployees: 'No employees found',
    loading: 'Loading...'
  }
}

// Sample employee data
const sampleEmployees = [
  {
    id: 1,
    name: 'أحمد حسن',
    nameEn: 'Ahmed Hassan',
    position: 'مدير الموارد البشرية',
    positionEn: 'HR Manager',
    department: 'الموارد البشرية',
    departmentEn: 'Human Resources',
    email: '<EMAIL>',
    phone: '+966 50 123 4567',
    hireDate: '2020-01-15',
    status: 'active',
    avatar: 'AH'
  },
  {
    id: 2,
    name: 'فاطمة علي',
    nameEn: 'Fatima Ali',
    position: 'مطور برمجيات',
    positionEn: 'Software Developer',
    department: 'تكنولوجيا المعلومات',
    departmentEn: 'Information Technology',
    email: '<EMAIL>',
    phone: '+966 50 234 5678',
    hireDate: '2021-03-10',
    status: 'active',
    avatar: 'FA'
  },
  {
    id: 3,
    name: 'عمر سالم',
    nameEn: 'Omar Salem',
    position: 'محلل مالي',
    positionEn: 'Financial Analyst',
    department: 'المالية',
    departmentEn: 'Finance',
    email: '<EMAIL>',
    phone: '+966 50 345 6789',
    hireDate: '2019-07-22',
    status: 'active',
    avatar: 'OS'
  },
  {
    id: 4,
    name: 'سارة محمود',
    nameEn: 'Sara Mahmoud',
    position: 'أخصائي تسويق',
    positionEn: 'Marketing Specialist',
    department: 'التسويق',
    departmentEn: 'Marketing',
    email: '<EMAIL>',
    phone: '+966 50 456 7890',
    hireDate: '2022-02-14',
    status: 'active',
    avatar: 'SM'
  },
  {
    id: 5,
    name: 'خالد إبراهيم',
    nameEn: 'Khalid Ibrahim',
    position: 'مدير العمليات',
    positionEn: 'Operations Manager',
    department: 'العمليات',
    departmentEn: 'Operations',
    email: '<EMAIL>',
    phone: '+966 50 567 8901',
    hireDate: '2020-11-08',
    status: 'active',
    avatar: 'KI'
  }
]

export default function Employees({ language }: EmployeesProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [employees, setEmployees] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const t = translations[language]

  // Fetch employees from API
  useEffect(() => {
    const fetchEmployees = async () => {
      try {
        setLoading(true)
        const response = await employeeAPI.getAll()
        setEmployees(response.data)
      } catch (error) {
        console.error('Error fetching employees:', error)
        // Fallback to sample data if API fails
        setEmployees(sampleEmployees)
      } finally {
        setLoading(false)
      }
    }

    fetchEmployees()
  }, [])

  const handleAddEmployee = () => {
    setShowAddModal(true)
  }

  const handleModalClose = () => {
    setShowAddModal(false)
  }

  const [newEmployeeData, setNewEmployeeData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    position: '',
    department: ''
  })

  const handleEmployeeAdded = async () => {
    try {
      setLoading(true)

      // Prepare data for API
      const employeeData = {
        first_name: newEmployeeData.first_name,
        last_name: newEmployeeData.last_name,
        email: newEmployeeData.email,
        phone: newEmployeeData.phone,
        position: newEmployeeData.position,
        department_id: 1, // Default department - you might want to make this selectable
        hire_date: new Date().toISOString().split('T')[0],
        employment_status: 'FULL_TIME' as const,
        gender: 'M' as const, // Default - you might want to make this selectable
        employee_id: `EMP${Date.now()}`, // Generate unique employee ID
        salary: 0
      }

      // Create employee via API
      const newEmployee = await employeeAPI.create(employeeData)

      // Refresh the employee list
      const response = await employeeAPI.getAll()
      setEmployees(response.data)

      // Reset form and close modal
      setNewEmployeeData({
        first_name: '',
        last_name: '',
        email: '',
        phone: '',
        position: '',
        department: ''
      })
      setShowAddModal(false)
    } catch (error) {
      console.error('Error creating employee:', error)
      alert('Error creating employee. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const filteredEmployees = employees.filter(employee => {
    const fullName = `${employee.user?.first_name || ''} ${employee.user?.last_name || ''}`.trim()
    const arabicName = `${employee.first_name_ar || ''} ${employee.last_name_ar || ''}`.trim()
    const displayName = language === 'ar' ? arabicName || fullName : fullName
    const email = employee.user?.email || employee.email || ''

    return displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
           email.toLowerCase().includes(searchTerm.toLowerCase())
  })

  const stats = [
    {
      title: t.totalEmployees,
      value: employees.length.toString(),
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: t.activeEmployees,
      value: employees.filter(emp => emp.status === 'active').length.toString(),
      icon: User,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      title: t.newHires,
      value: employees.filter(emp =>
        new Date(emp.hireDate) > new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
      ).length.toString(),
      icon: Calendar,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-4xl font-bold text-white drop-shadow-lg">
            {t.employees}
          </h1>
          <p className="text-white/80 mt-2 text-lg">
            {language === 'ar' ? 'إدارة بيانات الموظفين' : 'Manage employee information'}
          </p>
        </div>
        <Button
          onClick={handleAddEmployee}
          className="gradient-bg-blue hover:scale-105 transform transition-all duration-300 glow-hover text-white font-semibold px-6 py-3"
        >
          <Plus className="h-5 w-5 mr-2" />
          {t.addEmployee}
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {stats.map((stat, index) => (
          <Card key={index} className="modern-card border-0 hover:scale-105 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-white/80">
                    {stat.title}
                  </p>
                  <p className="text-3xl font-bold text-white mt-2">
                    {stat.value}
                  </p>
                </div>
                <div className="p-4 rounded-2xl gradient-bg-blue glow floating">
                  <stat.icon className="h-7 w-7 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search and Filter */}
      <Card className="modern-card border-0">
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Label htmlFor="search" className="text-white/90 font-medium">{t.searchEmployees}</Label>
              <div className="relative mt-2">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 h-5 w-5" />
                <Input
                  id="search"
                  placeholder={t.searchPlaceholder}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="glass text-white placeholder-white/60 border-white/30 focus:border-white/50 pl-12 py-3"
                />
              </div>
            </div>
            <div className="flex gap-2 items-end">
              <Button className="glass text-white border-white/30 hover:bg-white/20 px-6 py-3">
                <Filter className="h-5 w-5 mr-2" />
                {t.filter}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Employee List */}
      <Card className="modern-card border-0">
        <CardHeader>
          <CardTitle className="text-white text-xl">{t.employeeList}</CardTitle>
          <CardDescription className="text-white/70">
            {language === 'ar'
              ? `${filteredEmployees.length} من ${employees.length} موظف`
              : `${filteredEmployees.length} of ${employees.length} employees`
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-white/20">
                  <th className="text-left py-4 px-4 font-medium text-white/90">
                    {t.name}
                  </th>
                  <th className="text-left py-4 px-4 font-medium text-white/90">
                    {t.position}
                  </th>
                  <th className="text-left py-4 px-4 font-medium text-white/90">
                    {t.department}
                  </th>
                  <th className="text-left py-4 px-4 font-medium text-white/90">
                    {t.email}
                  </th>
                  <th className="text-left py-4 px-4 font-medium text-white/90">
                    {t.status}
                  </th>
                  <th className="text-left py-4 px-4 font-medium text-white/90">
                    {t.actions}
                  </th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan={6} className="py-12 text-center">
                      <div className="text-white/60">{t.loading}</div>
                    </td>
                  </tr>
                ) : (
                  filteredEmployees.map((employee) => {
                    const fullName = `${employee.user?.first_name || ''} ${employee.user?.last_name || ''}`.trim()
                    const arabicName = `${employee.first_name_ar || ''} ${employee.last_name_ar || ''}`.trim()
                    const displayName = language === 'ar' ? arabicName || fullName : fullName
                    const email = employee.user?.email || employee.email || ''
                    const avatar = `${employee.user?.first_name?.charAt(0) || 'N'}${employee.user?.last_name?.charAt(0) || 'A'}`

                    return (
                      <tr key={employee.id} className="border-b border-white/10 hover:glass transition-all duration-300">
                        <td className="py-4 px-4">
                          <div className="flex items-center gap-3">
                            <div className="w-12 h-12 gradient-bg-green rounded-full flex items-center justify-center text-white font-medium glow-green floating">
                              {avatar}
                            </div>
                            <div>
                              <p className="font-medium text-white">
                                {displayName}
                              </p>
                              <p className="text-sm text-white/60">
                                {email}
                              </p>
                            </div>
                          </div>
                        </td>
                        <td className="py-4 px-4 text-white/90">
                          {language === 'ar' ? employee.position_ar || employee.position : employee.position}
                        </td>
                        <td className="py-4 px-4 text-white/90">
                          {language === 'ar' ? employee.department_name_ar || employee.department_name : employee.department_name}
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex items-center gap-2 text-white/70">
                            <Mail className="h-4 w-4" />
                            {email}
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium glass ${
                            employee.is_active
                              ? 'text-green-300 border border-green-400/30'
                              : 'text-red-300 border border-red-400/30'
                          }`}>
                            {employee.is_active ? t.active : t.inactive}
                          </span>
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex items-center gap-2">
                            <Button variant="ghost" size="sm" className="glass text-white hover:bg-white/20 p-2">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm" className="glass text-red-300 hover:bg-red-500/20 p-2">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    )
                  })
                )}
              </tbody>
            </table>
          </div>

          {filteredEmployees.length === 0 && (
            <div className="text-center py-12">
              <Users className="h-16 w-16 text-white/40 mx-auto mb-4 floating" />
              <p className="text-white/60 text-lg">{t.noEmployees}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Employee Modal */}
      <Dialog open={showAddModal} onOpenChange={setShowAddModal}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-white">
              {t.addEmployee}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4 p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="first_name" className="text-white">
                  {language === 'ar' ? 'الاسم الأول' : 'First Name'}
                </Label>
                <Input
                  id="first_name"
                  value={newEmployeeData.first_name}
                  onChange={(e) => setNewEmployeeData({...newEmployeeData, first_name: e.target.value})}
                  className="glass text-white placeholder-white/60 border-white/30"
                  placeholder={language === 'ar' ? 'أدخل الاسم الأول' : 'Enter first name'}
                />
              </div>
              <div>
                <Label htmlFor="last_name" className="text-white">
                  {language === 'ar' ? 'الاسم الأخير' : 'Last Name'}
                </Label>
                <Input
                  id="last_name"
                  value={newEmployeeData.last_name}
                  onChange={(e) => setNewEmployeeData({...newEmployeeData, last_name: e.target.value})}
                  className="glass text-white placeholder-white/60 border-white/30"
                  placeholder={language === 'ar' ? 'أدخل الاسم الأخير' : 'Enter last name'}
                />
              </div>
              <div>
                <Label htmlFor="email" className="text-white">
                  {language === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={newEmployeeData.email}
                  onChange={(e) => setNewEmployeeData({...newEmployeeData, email: e.target.value})}
                  className="glass text-white placeholder-white/60 border-white/30"
                  placeholder={language === 'ar' ? 'أدخل البريد الإلكتروني' : 'Enter email'}
                />
              </div>
              <div>
                <Label htmlFor="phone" className="text-white">
                  {language === 'ar' ? 'رقم الهاتف' : 'Phone'}
                </Label>
                <Input
                  id="phone"
                  value={newEmployeeData.phone}
                  onChange={(e) => setNewEmployeeData({...newEmployeeData, phone: e.target.value})}
                  className="glass text-white placeholder-white/60 border-white/30"
                  placeholder={language === 'ar' ? 'أدخل رقم الهاتف' : 'Enter phone number'}
                />
              </div>
              <div>
                <Label htmlFor="position" className="text-white">
                  {language === 'ar' ? 'المنصب' : 'Position'}
                </Label>
                <Input
                  id="position"
                  value={newEmployeeData.position}
                  onChange={(e) => setNewEmployeeData({...newEmployeeData, position: e.target.value})}
                  className="glass text-white placeholder-white/60 border-white/30"
                  placeholder={language === 'ar' ? 'أدخل المنصب' : 'Enter position'}
                />
              </div>
              <div>
                <Label htmlFor="department" className="text-white">
                  {language === 'ar' ? 'القسم' : 'Department'}
                </Label>
                <Input
                  id="department"
                  value={newEmployeeData.department}
                  onChange={(e) => setNewEmployeeData({...newEmployeeData, department: e.target.value})}
                  className="glass text-white placeholder-white/60 border-white/30"
                  placeholder={language === 'ar' ? 'أدخل القسم' : 'Enter department'}
                />
              </div>
            </div>

            <div className="flex gap-4 pt-4">
              <Button
                onClick={handleEmployeeAdded}
                className="gradient-bg-blue flex-1"
              >
                {language === 'ar' ? 'حفظ' : 'Save'}
              </Button>
              <Button
                onClick={handleModalClose}
                variant="outline"
                className="glass text-white border-white/30 flex-1"
              >
                {language === 'ar' ? 'إلغاء' : 'Cancel'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
