/**
 * Generic CRUD Modal Component
 * Reusable modal for Create/Update operations across all entities
 */

import React, { useState, useEffect } from 'react'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { X } from 'lucide-react'

export type FormFieldValue = string | number | boolean | File | null | undefined

// ENHANCED: FormField interface with better validation and UX options
export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'date' | 'datetime-local' | 'textarea' | 'select' | 'checkbox' | 'file' | 'tel'
  required?: boolean
  placeholder?: string
  options?: { value: string; label: string }[]
  validation?: (value: FormFieldValue) => string | null
  disabled?: boolean
  loading?: boolean // ENHANCED: Loading state for select fields
  description?: string
  hint?: string // ENHANCED: Helpful hints for users
  helpText?: string // ENHANCED: Help text for complex fields
  min?: number
  max?: number
  step?: number
  accept?: string // for file inputs
  rows?: number // for textarea
  minLength?: number // ENHANCED: Minimum text length
  maxLength?: number // ENHANCED: Maximum text length
  defaultValue?: FormFieldValue // ENHANCED: Default value for new records
  section?: string // ENHANCED: Section grouping for better organization
  customAction?: { // ENHANCED: Custom action button for fields (e.g., retry)
    label: string
    onClick: () => void
    icon?: React.ComponentType<{ className?: string }>
    variant?: 'primary' | 'secondary' | 'danger'
  }
}

export type FormData = Record<string, FormFieldValue>

export interface CrudModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (data: FormData) => Promise<void>
  title: string
  fields: FormField[]
  initialData?: FormData
  language: 'ar' | 'en'
  loading?: boolean
  maxWidth?: string
  mode?: 'create' | 'edit' | 'view'
}

const translations = {
  ar: {
    save: 'حفظ',
    cancel: 'إلغاء',
    saving: 'جاري الحفظ...',
    required: 'هذا الحقل مطلوب',
    invalidEmail: 'البريد الإلكتروني غير صحيح',
    invalidNumber: 'الرقم غير صحيح',
    selectOption: 'اختر خيار',
    browse: 'تصفح',
    noFileSelected: 'لم يتم اختيار ملف'
  },
  en: {
    save: 'Save',
    cancel: 'Cancel',
    saving: 'Saving...',
    required: 'This field is required',
    invalidEmail: 'Invalid email address',
    invalidNumber: 'Invalid number',
    selectOption: 'Select option',
    browse: 'Browse',
    noFileSelected: 'No file selected'
  }
}

export default function CrudModal({
  isOpen,
  onClose,
  onSave,
  title,
  fields,
  initialData,
  language,
  loading = false,
  maxWidth = 'max-w-2xl',
  mode = 'create'
}: CrudModalProps): React.ReactElement {
  const [formData, setFormData] = useState<FormData>({})
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'success' | 'error'>('idle')
  const [saveMessage, setSaveMessage] = useState<string>('')

  const t = translations[language]
  const isRTL = language === 'ar'
  const isViewMode = mode === 'view'

  // Initialize form data
  // FIXED: Preserve existing form data when only field options change
  useEffect(() => {
    if (isOpen) {
      setFormData(prevFormData => {
        const initialFormData: FormData = {}
        let hasChanges = false

        fields.forEach((field) => {
          if (initialData && initialData[field.name] !== undefined) {
            initialFormData[field.name] = initialData[field.name]
          } else if (prevFormData[field.name] !== undefined) {
            // FIXED: Preserve existing form data if it exists
            initialFormData[field.name] = prevFormData[field.name]
          } else {
            // Set default values based on field type
            switch (field.type) {
              case 'checkbox':
                initialFormData[field.name] = false
                break
              case 'number':
                initialFormData[field.name] = field.min || 0
                break
              case 'date':
              case 'datetime-local':
                initialFormData[field.name] = ''
                break
              default:
                initialFormData[field.name] = ''
            }
          }

          // Check if this field value changed
          if (prevFormData[field.name] !== initialFormData[field.name]) {
            hasChanges = true
          }
        })

        // Only update if there are actual changes or if it's a fresh modal
        if (hasChanges || Object.keys(prevFormData).length === 0) {
          return initialFormData
        }

        return prevFormData
      })

      setErrors({})
      setSaveStatus('idle')
      setSaveMessage('')
    }
  }, [isOpen, initialData, fields])

  // Handle input change
  // ENHANCED: Real-time validation and input handling
  const handleInputChange = (fieldName: string, value: FormFieldValue): void => {
    setFormData((prev: FormData) => ({
      ...prev,
      [fieldName]: value
    }))

    // Clear error when user starts typing
    if (errors[fieldName]) {
      setErrors(prev => ({
        ...prev,
        [fieldName]: ''
      }))
    }

    // ENHANCED: Real-time validation for specific field types
    const field = fields.find(f => f.name === fieldName)
    if (field && value !== undefined && value !== null && value !== '') {
      const fieldError = validateSingleField(field, value)
      if (fieldError) {
        setErrors(prev => ({ ...prev, [fieldName]: fieldError }))
      }
    }
  }

  // ENHANCED: Single field validation function
  const validateSingleField = (field: FormField, value: FormFieldValue): string | null => {
    // Skip validation for empty optional fields
    if (!field.required && (!value || value.toString().trim() === '')) {
      return null
    }

    // Required field validation
    if (field.required && (!value || value.toString().trim() === '')) {
      return language === 'ar'
        ? `${field.label} مطلوب`
        : `${field.label} is required`
    }

    // Type-specific validation for non-empty values
    if (value && value.toString().trim() !== '') {
      switch (field.type) {
        case 'email':
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
          if (!emailRegex.test(value.toString())) {
            return language === 'ar'
              ? 'البريد الإلكتروني غير صحيح'
              : 'Invalid email format'
          }
          break

        case 'number':
          const numValue = parseFloat(value.toString())
          if (isNaN(numValue)) {
            return language === 'ar'
              ? 'يجب أن يكون رقم صحيح'
              : 'Must be a valid number'
          }
          if (field.min !== undefined && numValue < field.min) {
            return language === 'ar'
              ? `يجب أن يكون أكبر من أو يساوي ${field.min}`
              : `Must be greater than or equal to ${field.min}`
          }
          if (field.max !== undefined && numValue > field.max) {
            return language === 'ar'
              ? `يجب أن يكون أقل من أو يساوي ${field.max}`
              : `Must be less than or equal to ${field.max}`
          }
          break

        case 'date':
          const dateValue = new Date(value.toString())
          if (isNaN(dateValue.getTime())) {
            return language === 'ar'
              ? 'تاريخ غير صحيح'
              : 'Invalid date format'
          }
          break

        case 'text':
        case 'textarea':
          const textValue = value.toString()
          if (field.minLength && textValue.length < field.minLength) {
            return language === 'ar'
              ? `يجب أن يكون على الأقل ${field.minLength} أحرف`
              : `Must be at least ${field.minLength} characters`
          }
          if (field.maxLength && textValue.length > field.maxLength) {
            return language === 'ar'
              ? `يجب أن يكون أقل من ${field.maxLength} حرف`
              : `Must be less than ${field.maxLength} characters`
          }
          break
      }
    }

    return null
  }

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    fields.forEach(field => {
      const value = formData[field.name]

      // Required validation
      if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
        newErrors[field.name] = t.required
        return
      }

      // Custom validation
      if (field.validation && value) {
        const validationError = field.validation(value)
        if (validationError) {
          newErrors[field.name] = validationError
          return
        }
      }

      // Type-specific validation
      if (value) {
        switch (field.type) {
          case 'email':
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
            if (typeof value === 'string' && !emailRegex.test(value)) {
              newErrors[field.name] = t.invalidEmail
            }
            break
          case 'number':
            if (isNaN(Number(value))) {
              newErrors[field.name] = t.invalidNumber
            } else {
              const numValue = Number(value)
              if (field.min !== undefined && numValue < field.min) {
                newErrors[field.name] = `Minimum value is ${field.min}`
              }
              if (field.max !== undefined && numValue > field.max) {
                newErrors[field.name] = `Maximum value is ${field.max}`
              }
            }
            break
        }
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    setSaveStatus('saving')
    setSaveMessage('')
    setErrors({}) // Clear previous errors

    try {
      // Process form data
      const processedData = { ...formData }

      console.log('🔧 CrudModal: Processing form data')
      fields.forEach(field => {
        if (field.type === 'number' && processedData[field.name]) {
          processedData[field.name] = Number(processedData[field.name])
        }
      })

      console.log('📤 CrudModal: Calling onSave with processed data:', processedData)
      await onSave(processedData)
      console.log('✅ CrudModal: Save successful')

      // Show success message
      setSaveStatus('success')
      setSaveMessage(language === 'ar'
        ? (mode === 'create' ? 'تم إنشاء البيانات بنجاح' : 'تم تحديث البيانات بنجاح')
        : (mode === 'create' ? 'Data created successfully' : 'Data updated successfully')
      )

      // Close modal after a short delay to show success message
      setTimeout(() => {
        onClose()
        setSaveStatus('idle')
        setSaveMessage('')
      }, 1500)
    } catch (error) {
      console.error('❌ CrudModal: Error saving data:', error)
      setSaveStatus('error')

      // FIXED: Enhanced error handling with API validation error parsing
      let errorMessage = language === 'ar'
        ? 'حدث خطأ أثناء الحفظ. يرجى المحاولة مرة أخرى.'
        : 'An error occurred while saving. Please try again.'

      if (error instanceof Error) {
        // Try to parse API validation errors
        try {
          const errorData = JSON.parse(error.message)
          if (typeof errorData === 'object') {
            // Handle field-specific validation errors from Django REST Framework
            const fieldErrors: Record<string, string> = {}
            Object.keys(errorData).forEach(key => {
              if (Array.isArray(errorData[key])) {
                fieldErrors[key] = errorData[key][0] // Take first error message
              } else if (typeof errorData[key] === 'string') {
                fieldErrors[key] = errorData[key]
              }
            })

            if (Object.keys(fieldErrors).length > 0) {
              setErrors(fieldErrors)
              setSaveMessage(language === 'ar'
                ? 'يرجى تصحيح الأخطاء في النموذج'
                : 'Please correct the errors in the form')
              return
            }
          }
        } catch (parseError) {
          // If not JSON, check if it's a string error message
          if (error.message && error.message.includes('{')) {
            try {
              // Try to extract JSON from error message
              const jsonMatch = error.message.match(/\{.*\}/)
              if (jsonMatch) {
                const errorData = JSON.parse(jsonMatch[0])
                const fieldErrors: Record<string, string> = {}
                Object.keys(errorData).forEach(key => {
                  if (Array.isArray(errorData[key])) {
                    fieldErrors[key] = errorData[key][0]
                  } else if (typeof errorData[key] === 'string') {
                    fieldErrors[key] = errorData[key]
                  }
                })

                if (Object.keys(fieldErrors).length > 0) {
                  setErrors(fieldErrors)
                  return
                }
              }
            } catch (e) {
              // Fall through to use error message directly
            }
          }

          // Use the error message directly if available
          errorMessage = error.message || errorMessage
        }
      }

      setErrors({ general: errorMessage })
      setSaveMessage(errorMessage)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Render read-only view field
  const renderViewField = (field: FormField): JSX.Element => {
    const value = initialData?.[field.name] || ''

    const formatValue = (val: FormFieldValue): string => {
      if (val === null || val === undefined || val === '') return 'غير محدد'
      if (field.type === 'checkbox') return val ? 'نعم' : 'لا'
      if (field.type === 'select' && field.options) {
        const option = field.options.find(opt => opt.value === val)
        return option ? option.label : String(val)
      }
      if (val instanceof File) return val.name
      return String(val)
    }

    return (<div className="space-y-2">
        <Label className="text-white/90 font-medium">{field.label}</Label>
        <div className="glass-input bg-white/5 border-white/20 text-white/80 cursor-default">
          {formatValue(value)}
        </div>
      </div>
    )
  }

  // Render form field
  const renderField = (field: FormField): JSX.Element => {
    const value = formData[field.name] || ''
    const error = errors[field.name]

    const commonProps = {
      id: field.name,
      disabled: field.disabled || isSubmitting,
      className: `glass-input ${error ? 'border-red-500' : ''}`,
      'aria-label': field.label,
      'aria-required': field.required || false,
      'aria-invalid': !!error,
      'aria-describedby': error ? `${field.name}-error` : undefined
    }

    switch (field.type) {
      case 'textarea':
        return (<Textarea
            {...commonProps}
            value={typeof value === 'string' || typeof value === 'number' ? String(value) : ''}
            onChange={(e: any) => handleInputChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            rows={field.rows || 3}
          />
        )

      case 'select':
        const selectedOption = field.options?.find(opt => opt.value === value)
        const isSelectLoading = field.loading || false
        const hasOptions = field.options && field.options.length > 0

        return (<div className="space-y-2">
            <Select
              value={typeof value === 'string' ? value : String(value || '')}
              onValueChange={(newValue) => handleInputChange(field.name, newValue)}
              disabled={field.disabled || isSubmitting || isSelectLoading}
            >
              <SelectTrigger className={`glass-input ${error ? 'border-red-500' : ''}`}>
                <SelectValue placeholder={field.placeholder || t.selectOption}>
                  {isSelectLoading ? (<div className="flex items-center gap-2">
                      <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-(8V0C5).373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span>{language === 'ar' ? 'جاري التحميل...' : 'Loading...'}</span>
                    </div>
                  ) : selectedOption ? (selectedOption.label
                  ) : (field.placeholder || t.selectOption
                  )}
                </SelectValue>
              </SelectTrigger>
              <SelectContent className="glass-card border-white/20">
                {isSelectLoading ? (<div className="p-4 text-center text-white/60">
                    <div className="flex items-center justify-center gap-2">
                      <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-(8V0C5).373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span>{language === 'ar' ? 'جاري التحميل...' : 'Loading...'}</span>
                    </div>
                  </div>
                ) : !hasOptions ? (
                  <div className="p-4 text-center text-white/60">
                    <span>{language === 'ar' ? 'لا توجد خيارات متاحة' : 'No options available'}</span>
                  </div>
                ) : (
                  field.options?.map((option) => (<SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>

            {/* Show loading or error state below the select */}
            {isSelectLoading && (<p className="text-blue-300 text-sm flex items-center gap-2">
                <svg className="animate-spin h-3 w-3" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-(8V0C5).373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {language === 'ar' ? 'جاري تحميل الخيارات...' : 'Loading options...'}
              </p>
            )}

            {!isSelectLoading && !hasOptions && (<div className="flex items-center justify-between">
                <p className="text-yellow-300 text-sm">
                  {language === 'ar' ? 'لا توجد خيارات متاحة للاختيار' : 'No options available for selection'}
                </p>
                {field.customAction && (<Button
                    type="button"
                    size="sm"
                    variant="outline"
                    onClick={field.customAction.onClick}
                    className="glass-button text-xs"
                    disabled={isSelectLoading}
                  >
                    {field.customAction.icon && (() => {
                      const IconComponent = field.customAction.icon;
                      return <IconComponent className="h-3 w-3 mr-1" />;
                    })()}
                    {field.customAction.label}
                  </Button>
                )}
              </div>
            )}
          </div>
        )

      case 'checkbox':
        return (<div className="flex items-center space-x-2">
            <Checkbox
              id={field.name}
              checked={Boolean(value)}
              onCheckedChange={(checked) => handleInputChange(field.name, checked)}
              disabled={field.disabled || isSubmitting}
            />
            <Label htmlFor={field.name} className="text-white text-sm">
              {field.label}
            </Label>
          </div>
        )

      case 'file':
        return (
          <div className="space-y-2">
            <Input
              {...commonProps}
              type="file"
              onChange={(e: any) => handleInputChange(field.name, e.target.files?.[0])}
              accept={field.accept}
            />
            {!value && (<p className="text-white/60 text-sm">{t.noFileSelected}</p>
            )}
          </div>
        )

      default:
        return (<Input
            {...commonProps}
            type={field.type}
            value={typeof value === 'string' || typeof value === 'number' ? String(value) : ''}
            onChange={(e: any) => handleInputChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            min={field.min}
            max={field.max}
            step={field.step}
          />
        )
    }
  }

  // Calculate modal size based on number of fields
  const getModalSize = (): string => {
    const fieldCount = fields.length
    if (fieldCount <= 4) return 'md:max-w-lg lg:max-w-xl'
    if (fieldCount <= 8) return 'md:max-w-2xl lg:max-w-3xl'
    if (fieldCount <= 12) return 'md:max-w-3xl lg:max-w-4xl'
    return 'md:max-w-4xl lg:max-w-5xl xl:max-w-6xl'
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={`glass-card border-white/20 w-full max-w-[95vw] sm:max-w-[90vw] ${getModalSize()} max-h-[95vh] sm:max-h-[90vh] overflow-hidden ${isRTL ? 'rtl' : 'ltr'}`}>
        <DialogHeader className="pb-4 border-b border-white/10">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-white text-lg sm:text-xl font-semibold">{title}</DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-white/70 hover:text-white hover:bg-white/10 shrink-0"
              disabled={isSubmitting}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <DialogDescription className="text-white/70 text-sm">
            {isViewMode
              ? (language === 'ar' ? 'عرض تفاصيل البيانات' : 'View data details')
              : (language === 'ar' ? 'املأ النموذج أدناه لحفظ البيانات' : 'Fill out the form below to save the data')
            }
          </DialogDescription>

          {/* Status Message */}
          {saveMessage && (<div className={`mt-4 p-3 rounded-lg border ${
              saveStatus === 'success'
                ? 'bg-green-500/20 border-green-500/30 text-green-300'
                : saveStatus === 'error'
                ? 'bg-red-500/20 border-red-500/30 text-red-300'
                : 'bg-blue-500/20 border-blue-500/30 text-blue-300'
            }`}>
              <div className="flex items-center gap-2">
                {saveStatus === 'success' && (
                  <svg className="h-5 w-5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 (16zm3).707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                )}
                {saveStatus === 'error' && (
                  <svg className="h-5 w-5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                )}
                {saveStatus === 'saving' && (<svg className="animate-spin h-5 w-5 flex-shrink-0" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-(8V0C5).373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                )}
                <span className="text-sm font-medium">{saveMessage}</span>
              </div>
            </div>
          )}
        </DialogHeader>

        <div className="flex-1 overflow-y-auto max-h-[calc(95vh-200px)] sm:max-h-[calc(90vh-200px)]">
          {isViewMode ? (<div className="space-y-4 p-1">
              <div className={`grid gap-4 sm:gap-6 ${
                fields.length <= 4 ? 'grid-cols-1 sm:grid-cols-1 md:grid-cols-2' :
                fields.length <= 8 ? 'grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2' :
                fields.length <= 12 ? 'grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3' :
                'grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
              }`}>
                {fields.map((field) => (<div
                    key={field.name}
                    className={`${field.type === 'textarea' || field.type === 'checkbox' ? 'sm:col-span-1 md:col-span-2' : ''}`}
                  >
                    {renderViewField(field)}
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <form id="crud-form" onSubmit={handleSubmit} className="space-y-6 p-1">
              {(() => {
                // Group fields by section
                const fieldsBySection = fields.reduce((acc, field) => {
                  const section = field.section || 'General'
                  if (!acc[section]) acc[section] = []
                  acc[section].push(field)
                  return acc
                }, {} as Record<string, FormField[]>)
                return Object.entries(fieldsBySection).map(([sectionName, sectionFields]) => (<div key={sectionName} className="space-y-4">
                    {/* Section Header */}
                    {Object.keys(fieldsBySection).length > 1 && (
                      <div className="border-b border-white/10 pb-2">
                        <h3 className="text-lg font-semibold text-white">{sectionName}</h3>
                      </div>
                    )}

                    {/* Section Fields */}
                    <div className={`grid gap-4 sm:gap-6 ${
                      sectionFields.length <= 2 ? 'grid-cols-1 sm:grid-cols-1 md:grid-cols-2' :
                      sectionFields.length <= 4 ? 'grid-cols-1 sm:grid-cols-1 md:grid-cols-2' :
                      'grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
                    }`}>
                      {sectionFields.map((field) => (<div
                          key={field.name}
                          className={`${field.type === 'textarea' || field.type === 'checkbox' ? 'sm:col-span-1 md:col-span-2' : ''} space-y-2`}
                        >
                          {field.type !== 'checkbox' && (<Label htmlFor={field.name} className="text-white text-sm font-medium">
                              {field.label}
                              {field.required && <span className="text-red-400 ml-1">*</span>}
                            </Label>
                          )}

                          {renderField(field)}

                          {field.description && (<p className="text-white/60 text-sm mt-1">{field.description}</p>
                          )}

                          {/* Help text for complex fields */}
                          {field.helpText && (<p className="text-blue-300 text-sm mt-1">{field.helpText}</p>
                          )}

                          {/* ENHANCED: Better error display with icon and accessibility */}
                          {errors[field.name] && (<div
                              id={`${field.name}-error`}
                              className="flex items-center gap-2 text-red-400 text-sm mt-1"
                              role="alert"
                              aria-live="polite"
                            >
                              <svg className="h-4 w-4 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                              </svg>
                              <span>{errors[field.name]}</span>
                            </div>
                          )}

                          {/* ENHANCED: Show field hints for better UX */}
                          {!errors[field.name] && field.hint && (<p className="text-white/60 text-sm mt-1">{field.hint}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))
              })()}
            </form>
          )}
        </div>

        <DialogFooter className="grid grid-cols-1 sm:grid-cols-2 gap-2 pt-4 border-t border-white/10">
          {isViewMode ? (
            <Button
              type="button"
              onClick={onClose}
              className="glass-button bg-blue-500/30 hover:bg-blue-500/40 w-full sm:col-span-2"
            >
              {language === 'ar' ? 'إغلاق' : 'Close'}
            </Button>
          ) : (<>
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                className="glass-button w-full sm:w-auto order-2 sm:order-1"
                disabled={isSubmitting}
                aria-label={language === 'ar' ? 'إلغاء العملية' : 'Cancel operation'}
              >
                {t.cancel}
              </Button>
              <Button
                type="submit"
                form="crud-form"
                className={`glass-button w-full sm:w-auto order-1 sm:order-2 ${
                  saveStatus === 'success' ? 'bg-green-500/30 hover:bg-green-500/40' :
                  saveStatus === 'error' ? 'bg-red-500/30 hover:bg-red-500/40' :
                  'bg-blue-500/30 hover:bg-blue-500/40'
                }`}
                disabled={isSubmitting || loading || saveStatus === 'success'}
                aria-label={language === 'ar' ? 'حفظ البيانات' : 'Save data'}
              >
                <div className="flex items-center gap-2">
                  {(isSubmitting || loading || saveStatus === 'saving') && (<svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-(8V0C5).373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  )}
                  {saveStatus === 'success' && (<svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 (16zm3).707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  )}
                  <span>
                    {saveStatus === 'success'
                      ? (language === 'ar' ? 'تم الحفظ' : 'Saved')
                      : (isSubmitting || loading || saveStatus === 'saving')
                      ? t.saving
                      : t.save
                    }
                  </span>
                </div>
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
