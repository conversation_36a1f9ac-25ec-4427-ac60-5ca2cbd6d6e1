import React from 'react';
import * as React from "react"

import { cn } from "@/lib/utils"

interface SkeletonProps {
  // TODO: Define proper prop types
  [key: string]: any;
}

function Skeleton({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>): React.ReactElement {
  return (<div
      className={cn("animate-pulse rounded-md bg-muted", className)}
      {...props}
    />
  )
}

export { Skeleton }
