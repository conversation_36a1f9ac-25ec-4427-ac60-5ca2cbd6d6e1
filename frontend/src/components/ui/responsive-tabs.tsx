import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'

interface ScrollableTabsProps {
  // TODO: Define proper prop types
  [key: string]: any;
}

interface Tab {
  value: string
  label: string
  icon?: React.ReactNode
  content: React.ReactNode
}

interface ResponsiveTabsProps {
  tabs: Tab[]
  defaultValue?: string
  value?: string
  onValueChange?: (value: string) => void
  className?: string
}

export function ResponsiveTabs({
  tabs,
  defaultValue,
  value,
  onValueChange,
  className
}: ResponsiveTabsProps): React.ReactElement {
  const [internalActiveTab, setInternalActiveTab] = useState(defaultValue || tabs[0]?.value)

  // Use controlled value if provided, otherwise use internal state
  const currentTab: any = value !== undefined ? value : internalActiveTab

  const handleTabChange = (newValue: string): void => {
    // Update internal state only if not controlled
    if (value === undefined) {
      setInternalActiveTab(newValue)
    }
    // Always call the callback if provided
    onValueChange?.(newValue)
  }

  // Update internal state when defaultValue changes
  useEffect(() => {
    if (value === undefined && defaultValue && defaultValue !== internalActiveTab) {
      setInternalActiveTab(defaultValue)
    }
  }, [defaultValue, value, internalActiveTab])

  return (
    <div className={cn('w-full', className)}>
      {/* Mobile: Dropdown */}
      <div className="block sm:hidden mb-6">
        <select
          value={currentTab}
          onChange={(e: any) => handleTabChange(e.target.value)}
          className="w-full glass-input px-4 py-3 rounded-lg text-white bg-white/10 border border-white/20 focus:border-white/40 focus:outline-none"
        >
          {tabs.map((tab) => (
            <option key={tab.value} value={tab.value} className="bg-gray-800 text-white">
              {tab.label}
            </option>
          ))}
        </select>
      </div>

      {/* Desktop: Tab List */}
      <div className="hidden sm:block mb-6">
        <div className="glass-card border-white/20 p-1 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 w-full h-auto rounded-lg">
          {tabs.map((tab) => (
            <button
              key={tab.value}
              onClick={() => handleTabChange(tab.value)}
              className={cn(
                'flex items-center justify-center gap-2 px-3 py-3 rounded-md transition-all text-xs sm:text-sm',
                'hover:bg-white/10',
                currentTab === tab.value
                  ? 'bg-white/20 text-white'
                  : 'text-white/70'
              )}
            >
              {tab.icon && <span className="h-4 w-4 flex-shrink-0">{tab.icon}</span>}
              <span className="truncate">{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div className="w-full">
        {tabs.map((tab) => (
          <div
            key={tab.value}
            className={cn(
              'w-full',
              currentTab === tab.value ? 'block' : 'hidden'
            )}
          >
            {tab.content}
          </div>
        ))}
      </div>
    </div>
  )
}

// Alternative horizontal scrolling tabs for mobile
export function ScrollableTabs({
  tabs,
  defaultValue,
  value,
  onValueChange,
  className
}: ResponsiveTabsProps): React.ReactElement {
  const [internalActiveTab, setInternalActiveTab] = useState(defaultValue || tabs[0]?.value)

  // Use controlled value if provided, otherwise use internal state
  const currentTab = value !== undefined ? value : internalActiveTab

  const handleTabChange = (newValue: string): void => {
    // Update internal state only if not controlled
    if (value === undefined) {
      setInternalActiveTab(newValue)
    }
    // Always call the callback if provided
    onValueChange?.(newValue)
  }

  // Update internal state when defaultValue changes
  useEffect(() => {
    if (value === undefined && defaultValue && defaultValue !== internalActiveTab) {
      setInternalActiveTab(defaultValue)
    }
  }, [defaultValue, value, internalActiveTab])

  return (
    <div className={cn('w-full', className)}>
      {/* Horizontal Scrolling Tabs */}
      <div className="w-full overflow-x-auto mb-6 scrollbar-hide">
        <div className="glass-card border-white/20 p-1 flex w-full min-w-max sm:min-w-0 sm:grid sm:grid-cols-5 h-auto rounded-lg">
          {tabs.map((tab) => (
            <button
              key={tab.value}
              onClick={() => handleTabChange(tab.value)}
              className={cn(
                'flex items-center justify-center gap-1 sm:gap-2 px-3 py-3 rounded-md transition-all text-xs sm:text-sm whitespace-nowrap flex-shrink-0 sm:flex-shrink',
                'hover:bg-white/10',
                currentTab === tab.value
                  ? 'bg-white/20 text-white'
                  : 'text-white/70'
              )}
            >
              {tab.icon && <span className="h-4 w-4 flex-shrink-0">{tab.icon}</span>}
              <span className="block">{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div className="w-full">
        {tabs.map((tab) => (
          <div
            key={tab.value}
            className={cn(
              'w-full',
              currentTab === tab.value ? 'block' : 'hidden'
            )}
          >
            {tab.content}
          </div>
        ))}
      </div>
    </div>
  )
}

// Utility CSS classes to add to your global CSS
export const responsiveTabsStyles = `
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

@media (max-width: 640px) {
  .responsive-tabs-mobile {
    display: block;
  }
  
  .responsive-tabs-desktop {
    display: none;
  }
}

@media (min-width: 641px) {
  .responsive-tabs-mobile {
    display: none;
  }
  
  .responsive-tabs-desktop {
    display: block;
  }
}
`
